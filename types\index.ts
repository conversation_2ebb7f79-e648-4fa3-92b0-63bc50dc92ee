/**
 * 🃏 PetingGame 核心類型定義
 * 
 * 這個文件包含遊戲的所有核心數據類型和接口定義
 * 基於 docs/game_design.md 的設計規格
 */

// ==================== 基礎枚舉類型 ====================

/**
 * 卡牌稀有度等級 (1-5星)
 */
export enum CardRarity {
  COMMON = 1,     // ⭐ 普通 - 白光
  RARE = 2,       // ⭐⭐ 稀有 - 藍光
  EPIC = 3,       // ⭐⭐⭐ 史詩 - 紫光
  LEGENDARY = 4,  // ⭐⭐⭐⭐ 傳說 - 金光
  MYTHIC = 5      // ⭐⭐⭐⭐⭐ 神話 - 七彩光
}

/**
 * 卡牌種族系統
 */
export enum CardRace {
  HUMAN = 'Human',     // 人族 - 平衡屬性，藍色主題
  ELF = 'Elf',         // 精靈族 - 高速度魔攻，綠色主題
  ORC = 'Orc',         // 獸人族 - 高攻擊生命，橙色主題
  DRAGON = 'Dragon',   // 龍族 - 極高屬性，紅色主題
  ANGEL = 'Angel',     // 天使族 - 治療輔助，紫色主題
  DEMON = 'Demon'      // 惡魔族 - 詛咒減益，暗紅主題
}

/**
 * 戰鬥標籤分類
 */
export enum BattleTag {
  WARRIOR = 'Warrior',   // 戰士
  MAGE = 'Mage',         // 法師
  ARCHER = 'Archer',     // 射手
  TANK = 'Tank',         // 坦克
  HEALER = 'Healer',     // 治療
  SUPPORT = 'Support'    // 輔助
}

/**
 * 元素標籤分類
 */
export enum ElementTag {
  FIRE = 'Fire',     // 火
  WATER = 'Water',   // 水
  EARTH = 'Earth',   // 土
  AIR = 'Air',       // 風
  LIGHT = 'Light',   // 光
  DARK = 'Dark'      // 暗
}

/**
 * 特殊標籤分類
 */
export enum SpecialTag {
  ELITE = 'Elite',     // 精英
  BOSS = 'Boss',       // 首領
  MINION = 'Minion',   // 小兵
  FLYING = 'Flying',   // 飛行
  UNDEAD = 'Undead',   // 亡靈
  BEAST = 'Beast'      // 野獸
}

// 合併後的通用標籤型別
export type CombinedTag = BattleTag | ElementTag | SpecialTag;

/**
 * 技能目標類型
 */
export enum SkillTargetType {
  SELF = 'Self',                    // 自己
  SINGLE_ENEMY = 'SingleEnemy',     // 單個敵人
  ALL_ENEMIES = 'AllEnemies',       // 所有敵人
  SINGLE_ALLY = 'SingleAlly',       // 單個友軍
  ALL_ALLIES = 'AllAllies',         // 所有友軍
  RANDOM_ENEMY = 'RandomEnemy',     // 隨機敵人
  LOWEST_HP_ALLY = 'LowestHpAlly'   // 生命最低友軍
}

/**
 * 技能效果類型
 */
export enum SkillEffectType {
  DAMAGE = 'Damage',           // 傷害
  HEAL = 'Heal',               // 治療
  BUFF = 'Buff',               // 增益
  DEBUFF = 'Debuff',           // 減益
  SHIELD = 'Shield',           // 護盾
  DOT = 'DoT',                 // 持續傷害
  HOT = 'HoT'                  // 持續治療
}

/**
 * AI行為模式
 */
export enum AIBehaviorType {
  AGGRESSIVE = 'Aggressive',   // 攻擊型
  DEFENSIVE = 'Defensive',     // 防禦型
  BALANCED = 'Balanced'        // 平衡型
}

/**
 * 戰鬥狀態
 */
export enum BattleState {
  PREPARING = 'Preparing',     // 準備中
  FIGHTING = 'Fighting',       // 戰鬥中
  VICTORY = 'Victory',         // 勝利
  DEFEAT = 'Defeat',           // 失敗
  PAUSED = 'Paused'            // 暫停
}

// ==================== 核心數據接口 ====================

/**
 * 卡牌基礎屬性接口
 */
export interface CardStats {
  attack: number;        // 物理攻擊力
  magicAttack: number;   // 魔法攻擊力
  defense: number;       // 防禦
  critRate: number;      // 暴擊率 (0-1)
  health: number;        // 生命值
  speed: number;         // 行動速度
}

/**
 * 卡牌技能接口
 */
export interface CardSkill {
  id: string;                           // 技能ID
  name: string;                         // 技能名稱
  description: string;                  // 技能描述
  targetType: SkillTargetType;          // 目標類型
  effectType: SkillEffectType;          // 效果類型
  value: number;                        // 效果數值
  cooldown: number;                     // 冷卻時間 (技能不消耗魔力)
}

/**
 * 卡牌完整數據接口
 */
export interface Card {
  id: string;                           // 卡牌唯一ID
  configId: string;                     // 配置表ID
  name: string;                         // 卡牌名稱
  race: CardRace[];                     // 種族 (可多選)
  rarity: CardRarity;                   // 稀有度
  level: number;                        // 等級
  experience: number;                   // 經驗值
  stats: CardStats;                     // 基礎屬性 (含魔攻/防禦/暴擊率)
  tags: (BattleTag | ElementTag | SpecialTag)[]; // 合併後標籤
  skills: CardSkill[];                  // 技能列表
  imageUrl?: string;                    // 卡牌圖片
  isOwned: boolean;                     // 是否擁有
  obtainedAt?: Date;                    // 獲得時間
}

/**
 * 戰鬥中的卡牌狀態
 */
export interface BattleCard extends Card {
  currentHealth: number;                // 當前生命值
  actionBar: number;                    // 行動條進度 (0-100)
  buffs: StatusEffect[];                // 增益效果
  debuffs: StatusEffect[];              // 減益效果
  isAlive: boolean;                     // 是否存活
  position: { row: number; col: number }; // 戰場座標 (row 0-2, col 0-2)
  team: 'player' | 'enemy';             // 所屬隊伍
}

/**
 * 狀態效果接口
 */
export interface StatusEffect {
  id: string;                           // 效果ID
  name: string;                         // 效果名稱
  type: SkillEffectType;                // 效果類型
  value: number;                        // 效果數值
  duration: number;                     // 持續時間(秒或刻)
  stackable: boolean;                   // 是否可疊加
  iconUrl?: string;                     // 圖標URL
}

/**
 * 隊伍配置接口
 */
export interface Team {
  id: string;                           // 隊伍ID
  name: string;                         // 隊伍名稱
  cards: (Card | null)[][];             // 卡牌網格 (3x3，允許空位 null)
  totalPower: number;                   // 總戰力
  createdAt: Date;                      // 創建時間
  lastUsed: Date;                       // 最後使用時間
}

/**
 * 戰鬥結果接口
 */
export interface BattleResult {
  isVictory: boolean;                   // 是否勝利
  duration: number;                     // 戰鬥時長(秒)
  playerTeam: (BattleCard | null)[][];  // 玩家隊伍最終狀態 (3x3，允許空位 null)
  enemyTeam: (BattleCard | null)[][];   // 敵方隊伍最終狀態 (3x3，允許空位 null)
  rewards: BattleReward[];              // 戰鬥獎勵
  experience: number;                   // 獲得經驗
  gold: number;                         // 獲得金幣
}

/**
 * 戰鬥獎勵接口
 */
export interface BattleReward {
  type: 'card' | 'gold' | 'diamond' | 'experience';
  value: number | Card;                 // 獎勵內容
  rarity?: CardRarity;                  // 卡牌稀有度(僅卡牌獎勵)
}

// ==================== 遊戲系統接口 ====================

/**
 * 玩家數據接口
 */
export interface PlayerData {
  id: string;                           // 玩家ID
  name: string;                         // 玩家名稱
  level: number;                        // 玩家等級
  experience: number;                   // 玩家經驗
  gold: number;                         // 金幣
  diamonds: number;                     // 鑽石
  currentStage: number;                 // 當前關卡
  ownedCards: Card[];                   // 擁有的卡牌
  teams: Team[];                        // 隊伍配置
  currentTeamId: string;                // 當前使用隊伍ID
  settings: GameSettings;               // 遊戲設定
  statistics: PlayerStatistics;         // 統計數據
  lastLoginAt: Date;                    // 最後登入時間
  createdAt: Date;                      // 創建時間
}

/**
 * 遊戲設定接口
 */
export interface GameSettings {
  musicVolume: number;                  // 音樂音量 (0-1)
  soundVolume: number;                  // 音效音量 (0-1)
  battleSpeed: number;                  // 戰鬥速度 (1x, 2x, 4x)
  autoPlay: boolean;                    // 自動戰鬥
  showDamageNumbers: boolean;           // 顯示傷害數字
  enableHaptics: boolean;               // 啟用觸覺反饋
  language: 'zh-TW' | 'zh-CN' | 'en';   // 語言設定
}

/**
 * 玩家統計數據接口
 */
export interface PlayerStatistics {
  totalBattles: number;                 // 總戰鬥次數
  victories: number;                    // 勝利次數
  defeats: number;                      // 失敗次數
  totalPlayTime: number;                // 總遊戲時間(分鐘)
  cardsCollected: number;               // 收集卡牌數量
  highestStage: number;                 // 最高關卡
  totalGachaDraws: number;              // 總抽卡次數
  legendaryCards: number;               // 傳說卡牌數量
  mythicCards: number;                  // 神話卡牌數量
}

// ==================== 導出其他模塊類型 ====================

// 重新導出配置相關類型
export * from './config';

// 重新導出戰鬥相關類型
export * from './battle';

// 重新導出UI相關類型
export * from './ui';

// 重新導出抽卡相關類型
export * from './gacha';

// 重新導出工具函數
export * from './utils';

// 重新導出常量定義
export * from './constants';

