<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>手繪米色款 A｜Vintage Paper（互補配色）</title>
  <style>
    :root{
      /* Palette: warm beige base + burnt sienna (accent) + desaturated blue (complement) */
      --bg-0:#f7f1e3;   /* 背景紙 */
      --bg-1:#efe7d4;   /* 次層紙 */
      --paper-edge:#e4d6b9;
      --ink:#3b2b22;    /* 手寫墨色 */
      --accent:#c46a2b; /* 焦糖/赭石（攻擊）*/
      --accent-2:#5b7a8d; /* 互補的藍灰（提示/技能）*/
      --good:#2f6f4f;   /* 我方/治癒 */
      --bad:#a7302b;    /* 敵方/傷害 */
      --shadow:0 6px 16px rgba(59,43,34,.15);
      --dashed:2px dashed rgba(59,43,34,.35);
      --radius:10px;
    }
    *{box-sizing:border-box}
    body{margin:0;font-family:"Noto Serif TC", ui-serif, Georgia, serif;background:
      radial-gradient(1200px 800px at 10% -20%, rgba(255,255,255,.7), transparent 50%),
      radial-gradient(800px 600px at 110% 10%, rgba(255,255,255,.35), transparent 55%),
      linear-gradient(180deg, var(--bg-0), var(--bg-1));
      color:var(--ink);
    }
    /* 紙張纖維感 */
    body:before{content:"";position:fixed;inset:0;pointer-events:none;opacity:.25;
      background:
        radial-gradient(circle at 20% 30%, rgba(59,43,34,.08) 1px, transparent 1px) 0 0/22px 22px,
        radial-gradient(circle at 70% 60%, rgba(59,43,34,.06) 1px, transparent 1px) 0 0/18px 18px;
      mix-blend-mode:multiply;}

    .wrap{max-width:390px;margin:0 auto;min-height:100vh;display:flex;flex-direction:column}

    /* 頂部 */
    header{position:sticky;top:0;z-index:10;background:rgba(239,231,212,.8);backdrop-filter:blur(6px);
      border-bottom:var(--dashed);padding:10px 12px;display:flex;align-items:center;justify-content:space-between}
    .stage{font-weight:700;font-size:16px;transform:rotate(-1.5deg)}
    .h-btn{width:36px;height:36px;border:2px solid var(--ink);border-radius:9px;cursor:pointer;background:var(--accent);
      color:#fff;box-shadow:2px 2px 0 rgba(59,43,34,.25);transform:rotate(-2deg);}
    .h-btn:focus-visible{outline:3px dashed var(--ink);outline-offset:2px}

    /* 區塊標題 */
    .section{padding:12px 10px 14px}
    .section-title{display:flex;justify-content:space-between;font-weight:700;margin:0 2px 10px}
    .enemy{background:rgba(167,48,43,.06);border-bottom:var(--dashed)}
    .player{background:rgba(47,111,79,.06);border-bottom:var(--dashed)}

    /* 卡片格 - 3x3 grid */
    .grid{display:grid;grid-template-columns:repeat(3,1fr);gap:8px;justify-items:center}

    /* 手繪卡片 - 縮小以適應 3x3 */
    .card{width:88px;height:120px;background:linear-gradient(145deg,#fbf8f2,#efe9dc);border:3px solid var(--ink);
      border-radius:8px;box-shadow:var(--shadow);position:relative;padding:4px 3px;cursor:pointer;transition:.2s}
    .card:after{content:"";position:absolute;inset:4px;border:2px dashed var(--ink);opacity:.35;border-radius:6px}
    .card:hover{transform:translateY(-3px) rotate(.8deg)}
    .card.enemy{border-color:rgba(167,48,43,.85)}
    .card.player{border-color:rgba(47,111,79,.85)}
    .lvl{position:absolute;top:-6px;left:-6px;background:var(--accent);color:#fff;border:2px solid var(--ink);
      width:20px;height:20px;border-radius:50%;display:grid;place-items:center;font-weight:700;font-size:10px}
    .icon{font-size:22px;margin-top:10px;filter:sepia(.25) contrast(1.05)}
    .name{font-weight:700;font-size:10px;margin:4px 0 2px;text-align:center;text-decoration:underline;text-decoration-color:rgba(59,43,34,.25)}
    .stats{display:flex;gap:6px;justify-content:center;font-weight:700;font-size:9px}
    .atk{color:var(--accent)}
    .spd{color:var(--good)}
    .hp{width:92%;height:5px;background:rgba(59,43,34,.18);border:1px solid var(--ink);border-radius:3px;overflow:hidden;margin-top:4px}
    .hp>.fill{height:100%;background:linear-gradient(90deg, #dc4b3f, #a7302b)}
    .hp>.fill.player{background:linear-gradient(90deg,#2e7d4f,#1e5f3a)}

    /* 行動分隔線 */
    .action-divider{position:relative;padding:16px 20px;background:rgba(196,106,43,.08)}
    .action-line{height:3px;background:linear-gradient(90deg, transparent, var(--accent), transparent);border-radius:2px;position:relative}
    .action-icon{position:absolute;top:-12px;left:50%;transform:translateX(-50%);background:var(--bg-0);
      border:2px solid var(--accent);border-radius:50%;width:24px;height:24px;display:grid;place-items:center;
      font-size:14px;box-shadow:var(--shadow)}

    /* 底部導航 */
    .bottom-nav{position:sticky;bottom:0;background:rgba(239,231,212,.95);backdrop-filter:blur(10px);
      border-top:var(--dashed);padding:8px 0}
    .nav-tabs{display:flex;justify-content:space-around;align-items:center}
    .nav-tab{display:flex;flex-direction:column;align-items:center;gap:2px;padding:6px 8px;
      border-radius:8px;cursor:pointer;min-width:44px;min-height:44px;justify-content:center;
      color:var(--ink);border:2px solid transparent;transition:.2s;transform:rotate(-1deg)}
    .nav-tab:hover{background:var(--accent);border-color:var(--ink);color:#fff;
      transform:rotate(0) scale(1.05);box-shadow:2px 2px 0 rgba(59,43,34,.25)}
    .nav-tab.active{background:var(--accent);color:#fff;border-color:var(--ink);
      transform:rotate(1deg);box-shadow:2px 2px 0 rgba(59,43,34,.25)}
    .nav-icon{font-size:18px}
    .nav-label{font-size:9px;font-weight:700}

    /* 響應式 */
    @media (max-width:360px){
      .card{width:78px;height:108px}
      .icon{font-size:18px}
      .name{font-size:9px}
      .stats{font-size:8px}
      .nav-icon{font-size:16px}
      .nav-label{font-size:8px}
    }
  </style>
</head>
<body>
  <div class="wrap">
    <header>
      <div class="stage">✦ 第 1-1 關：森林小徑</div>
      <button class="h-btn" title="設定">⚙️</button>
    </header>

    <section class="section enemy">
      <div class="section-title"><span>🔴 ENEMY 9/9</span><span>❤️ 1890</span></div>
      <div class="grid">
        <div class="card enemy"><div class="lvl">4</div><div class="icon">🐺</div><div class="name">狼人</div><div class="stats"><span class="atk">⚔️65</span><span class="spd">⚡12</span></div><div class="hp"><div class="fill" style="width:80%"></div></div></div>
        <div class="card enemy"><div class="lvl">5</div><div class="icon">🧌</div><div class="name">食人魔</div><div class="stats"><span class="atk">⚔️85</span><span class="spd">⚡8</span></div><div class="hp"><div class="fill" style="width:70%"></div></div></div>
        <div class="card enemy"><div class="lvl">3</div><div class="icon">🕷️</div><div class="name">蜘蛛</div><div class="stats"><span class="atk">⚔️45</span><span class="spd">⚡18</span></div><div class="hp"><div class="fill" style="width:88%"></div></div></div>
        <div class="card enemy"><div class="lvl">2</div><div class="icon">🦇</div><div class="name">蝙蝠</div><div class="stats"><span class="atk">⚔️35</span><span class="spd">⚡22</span></div><div class="hp"><div class="fill" style="width:95%"></div></div></div>
        <div class="card enemy"><div class="lvl">6</div><div class="icon">🐉</div><div class="name">幼龍</div><div class="stats"><span class="atk">⚔️110</span><span class="spd">⚡14</span></div><div class="hp"><div class="fill" style="width:85%"></div></div></div>
        <div class="card enemy"><div class="lvl">4</div><div class="icon">🧙</div><div class="name">法師</div><div class="stats"><span class="atk">⚔️75</span><span class="spd">⚡16</span></div><div class="hp"><div class="fill" style="width:60%"></div></div></div>
        <div class="card enemy"><div class="lvl">3</div><div class="icon">🗡️</div><div class="name">劍士</div><div class="stats"><span class="atk">⚔️55</span><span class="spd">⚡20</span></div><div class="hp"><div class="fill" style="width:75%"></div></div></div>
        <div class="card enemy"><div class="lvl">5</div><div class="icon">🏹</div><div class="name">弓手</div><div class="stats"><span class="atk">⚔️70</span><span class="spd">⚡19</span></div><div class="hp"><div class="fill" style="width:82%"></div></div></div>
        <div class="card enemy"><div class="lvl">4</div><div class="icon">🛡️</div><div class="name">守衛</div><div class="stats"><span class="atk">⚔️50</span><span class="spd">⚡10</span></div><div class="hp"><div class="fill" style="width:90%"></div></div></div>
      </div>
    </section>

    <section class="action-divider">
      <div class="action-line">
        <div class="action-icon">⚡</div>
      </div>
    </section>

    <section class="section player">
      <div class="section-title"><span>🔵 YOUR TEAM 9/9</span><span>❤️ 2340</span></div>
      <div class="grid">
        <div class="card player"><div class="lvl">5</div><div class="icon">🔥</div><div class="name">火龍戰士</div><div class="stats"><span class="atk">⚔️120</span><span class="spd">⚡15</span></div><div class="hp"><div class="fill player" style="width:100%"></div></div></div>
        <div class="card player"><div class="lvl">3</div><div class="icon">🏹</div><div class="name">月光射手</div><div class="stats"><span class="atk">⚔️85</span><span class="spd">⚡18</span></div><div class="hp"><div class="fill player" style="width:90%"></div></div></div>
        <div class="card player"><div class="lvl">4</div><div class="icon">🛡️</div><div class="name">聖騎士</div><div class="stats"><span class="atk">⚔️75</span><span class="spd">⚡12</span></div><div class="hp"><div class="fill player" style="width:96%"></div></div></div>
        <div class="card player"><div class="lvl">6</div><div class="icon">🧙‍♀️</div><div class="name">冰霜法師</div><div class="stats"><span class="atk">⚔️95</span><span class="spd">⚡14</span></div><div class="hp"><div class="fill player" style="width:88%"></div></div></div>
        <div class="card player"><div class="lvl">5</div><div class="icon">🗡️</div><div class="name">劍聖</div><div class="stats"><span class="atk">⚔️110</span><span class="spd">⚡17</span></div><div class="hp"><div class="fill player" style="width:92%"></div></div></div>
        <div class="card player"><div class="lvl">4</div><div class="icon">🏥</div><div class="name">治療師</div><div class="stats"><span class="atk">⚔️45</span><span class="spd">⚡16</span></div><div class="hp"><div class="fill player" style="width:100%"></div></div></div>
        <div class="card player"><div class="lvl">3</div><div class="icon">🗡️</div><div class="name">盜賊</div><div class="stats"><span class="atk">⚔️65</span><span class="spd">⚡25</span></div><div class="hp"><div class="fill player" style="width:85%"></div></div></div>
        <div class="card player"><div class="lvl">5</div><div class="icon">🔮</div><div class="name">召喚師</div><div class="stats"><span class="atk">⚔️80</span><span class="spd">⚡13</span></div><div class="hp"><div class="fill player" style="width:94%"></div></div></div>
        <div class="card player"><div class="lvl">6</div><div class="icon">⚡</div><div class="name">雷電法師</div><div class="stats"><span class="atk">⚔️105</span><span class="spd">⚡19</span></div><div class="hp"><div class="fill player" style="width:98%"></div></div></div>
      </div>
    </section>

    <!-- 底部導航 -->
    <div class="bottom-nav">
      <div class="nav-tabs">
        <div class="nav-tab active">
          <div class="nav-icon">⚔️</div>
          <div class="nav-label">戰鬥</div>
        </div>
        <div class="nav-tab">
          <div class="nav-icon">🎴</div>
          <div class="nav-label">抽卡</div>
        </div>
        <div class="nav-tab">
          <div class="nav-icon">👥</div>
          <div class="nav-label">隊伍</div>
        </div>
        <div class="nav-tab">
          <div class="nav-icon">📊</div>
          <div class="nav-label">數據</div>
        </div>
        <div class="nav-tab">
          <div class="nav-icon">⚙️</div>
          <div class="nav-label">設定</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 底部導航交互
    document.querySelectorAll('.nav-tab').forEach(tab => {
      tab.addEventListener('click', function() {
        document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
      });
    });

    // 卡片懸停效果增強
    document.querySelectorAll('.card').forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.zIndex = '5';
      });
      card.addEventListener('mouseleave', function() {
        this.style.zIndex = '1';
      });
    });
  </script>
</body>
</html>

