<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>手繪米色款 A｜Vintage Paper（互補配色）</title>
  <style>
    :root{
      /* Palette: warm beige base + burnt sienna (accent) + desaturated blue (complement) */
      --bg-0:#f7f1e3;   /* 背景紙 */
      --bg-1:#efe7d4;   /* 次層紙 */
      --paper-edge:#e4d6b9;
      --ink:#3b2b22;    /* 手寫墨色 */
      --accent:#c46a2b; /* 焦糖/赭石（攻擊）*/
      --accent-2:#5b7a8d; /* 互補的藍灰（提示/技能）*/
      --good:#2f6f4f;   /* 我方/治癒 */
      --bad:#a7302b;    /* 敵方/傷害 */
      --shadow:0 6px 16px rgba(59,43,34,.15);
      --dashed:2px dashed rgba(59,43,34,.35);
      --radius:10px;
    }
    *{box-sizing:border-box}
    body{margin:0;font-family:"Noto Serif TC", ui-serif, Georgia, serif;background:
      radial-gradient(1200px 800px at 10% -20%, rgba(255,255,255,.7), transparent 50%),
      radial-gradient(800px 600px at 110% 10%, rgba(255,255,255,.35), transparent 55%),
      linear-gradient(180deg, var(--bg-0), var(--bg-1));
      color:var(--ink);
    }
    /* 紙張纖維感 */
    body:before{content:"";position:fixed;inset:0;pointer-events:none;opacity:.25;
      background:
        radial-gradient(circle at 20% 30%, rgba(59,43,34,.08) 1px, transparent 1px) 0 0/22px 22px,
        radial-gradient(circle at 70% 60%, rgba(59,43,34,.06) 1px, transparent 1px) 0 0/18px 18px;
      mix-blend-mode:multiply;}

    .wrap{max-width:390px;margin:0 auto;min-height:100vh;display:flex;flex-direction:column}

    /* 頂部 */
    header{position:sticky;top:0;z-index:10;background:rgba(239,231,212,.8);backdrop-filter:blur(6px);
      border-bottom:var(--dashed);padding:10px 12px;display:flex;align-items:center;justify-content:space-between}
    .stage{font-weight:700;font-size:16px;transform:rotate(-1.5deg)}
    .h-btn{width:36px;height:36px;border:2px solid var(--ink);border-radius:9px;cursor:pointer;background:var(--accent);
      color:#fff;box-shadow:2px 2px 0 rgba(59,43,34,.25);transform:rotate(-2deg);}
    .h-btn:focus-visible{outline:3px dashed var(--ink);outline-offset:2px}

    /* 區塊標題 */
    .section{padding:12px 10px 14px}
    .section-title{display:flex;justify-content:space-between;font-weight:700;margin:0 2px 10px}
    .enemy{background:rgba(167,48,43,.06);border-bottom:var(--dashed)}
    .player{background:rgba(47,111,79,.06);border-bottom:var(--dashed)}

    /* 卡片格 */
    .grid{display:grid;grid-template-columns:repeat(3,1fr);gap:10px;justify-items:center}

    /* 手繪卡片 */
    .card{width:102px;height:142px;background:linear-gradient(145deg,#fbf8f2,#efe9dc);border:3px solid var(--ink);
      border-radius:8px;box-shadow:var(--shadow);position:relative;padding:6px 5px;cursor:pointer;transition:.2s}
    .card:after{content:"";position:absolute;inset:6px;border:2px dashed var(--ink);opacity:.35;border-radius:6px}
    .card:hover{transform:translateY(-3px) rotate(.8deg)}
    .card.enemy{border-color:rgba(167,48,43,.85)}
    .card.player{border-color:rgba(47,111,79,.85)}
    .lvl{position:absolute;top:-6px;left:-6px;background:var(--accent);color:#fff;border:2px solid var(--ink);
      width:22px;height:22px;border-radius:50%;display:grid;place-items:center;font-weight:700}
    .icon{font-size:26px;margin-top:14px;filter:sepia(.25) contrast(1.05)}
    .name{font-weight:700;font-size:12px;margin:6px 0 2px;text-align:center;text-decoration:underline;text-decoration-color:rgba(59,43,34,.25)}
    .stats{display:flex;gap:8px;justify-content:center;font-weight:700;font-size:11px}
    .atk{color:var(--accent)}
    .spd{color:var(--good)}
    .hp{width:92%;height:6px;background:rgba(59,43,34,.18);border:1px solid var(--ink);border-radius:3px;overflow:hidden;margin-top:6px}
    .hp>.fill{height:100%;background:linear-gradient(90deg, #dc4b3f, #a7302b)}
    .hp>.fill.player{background:linear-gradient(90deg,#2e7d4f,#1e5f3a)}

    /* 回合區 */
    .action{background:rgba(196,106,43,.12);border-top:var(--dashed);border-bottom:var(--dashed);padding:10px 12px}
    .next{font-weight:700;text-align:center;margin-bottom:8px}
    .queue{display:flex;justify-content:center;gap:8px}
    .chip{width:32px;height:32px;border-radius:50%;border:2px solid var(--ink);display:grid;place-items:center;font-weight:700}
    .chip.p{background:var(--good);color:#fff;transform:rotate(-2deg)}
    .chip.e{background:var(--bad);color:#fff;transform:rotate(2deg)}
    .chip.active{outline:3px dashed var(--accent-2);outline-offset:2px}

    /* 底部控制 */
    .controls{position:sticky;bottom:0;background:rgba(239,231,212,.9);backdrop-filter:blur(8px);border-top:var(--dashed);
      padding:10px;display:flex;gap:10px}
    .btn{flex:1;padding:10px 8px;border:2px solid var(--ink);border-radius:9px;background:var(--accent);color:#fff;font-weight:700}
    .btn.alt{background:var(--bad)}

    /* 響應式 */
    @media (max-width:360px){.card{width:90px;height:124px}.icon{font-size:22px}.chip{width:28px;height:28px}}
  </style>
</head>
<body>
  <div class="wrap">
    <header>
      <div class="stage">✦ 第 1-1 關：森林小徑</div>
      <button class="h-btn" title="設定">⚙️</button>
    </header>

    <section class="section enemy">
      <div class="section-title"><span>🔴 ENEMY 3/3</span><span>❤️ 630</span></div>
      <div class="grid">
        <div class="card enemy"><div class="lvl">4</div><div class="icon">🐺</div><div class="name">狼人</div><div class="stats"><span class="atk">⚔️65</span><span class="spd">⚡12</span></div><div class="hp"><div class="fill" style="width:80%"></div></div></div>
        <div class="card enemy"><div class="lvl">5</div><div class="icon">🧌</div><div class="name">食人魔</div><div class="stats"><span class="atk">⚔️85</span><span class="spd">⚡8</span></div><div class="hp"><div class="fill" style="width:70%"></div></div></div>
        <div class="card enemy"><div class="lvl">3</div><div class="icon">🕷️</div><div class="name">蜘蛛</div><div class="stats"><span class="atk">⚔️45</span><span class="spd">⚡18</span></div><div class="hp"><div class="fill" style="width:88%"></div></div></div>
      </div>
    </section>

    <section class="action">
      <div class="next">⚡ 下一個：火龍戰士</div>
      <div class="queue"><div class="chip p active">你</div><div class="chip e">敵</div><div class="chip p">你</div><div class="chip e">敵</div></div>
    </section>

    <section class="section player">
      <div class="section-title"><span>🔵 YOUR TEAM 3/3</span><span>❤️ 780</span></div>
      <div class="grid">
        <div class="card player"><div class="lvl">5</div><div class="icon">🔥</div><div class="name">火龍戰士</div><div class="stats"><span class="atk">⚔️120</span><span class="spd">⚡15</span></div><div class="hp"><div class="fill player" style="width:100%"></div></div></div>
        <div class="card player"><div class="lvl">3</div><div class="icon">🏹</div><div class="name">月光射手</div><div class="stats"><span class="atk">⚔️85</span><span class="spd">⚡18</span></div><div class="hp"><div class="fill player" style="width:90%"></div></div></div>
        <div class="card player"><div class="lvl">4</div><div class="icon">🛡️</div><div class="name">聖騎士</div><div class="stats"><span class="atk">⚔️75</span><span class="spd">⚡12</span></div><div class="hp"><div class="fill player" style="width:96%"></div></div></div>
      </div>
    </section>

    <div class="controls">
      <button class="btn" aria-label="切換速度">⏩ 1x</button>
      <button class="btn alt" aria-label="暫停/繼續">⏸️ 暫停</button>
    </div>
  </div>
</body>
</html>

