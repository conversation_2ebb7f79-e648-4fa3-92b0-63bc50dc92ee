const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// 添加調試日誌
console.log('Metro Config: 載入配置');
console.log('Metro Config: Hermes 啟用狀態', config.transformer?.hermesParser);

// Enable hot reloading for web
config.server = {
  enhanceMiddleware: (middleware) => {
    console.log('Metro Config: 增強中介軟體以支持熱重載');
    return (req, res, next) => {
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Cache-Control', 'no-store');
      console.log('Metro Config: 請求處理', req.url);
      return middleware(req, res, next);
    };
  },
};

// 確保 Hermes 配置正確
config.transformer = {
  ...config.transformer,
  hermesParser: true,
  enableBabelRCLookup: false,
};

console.log('Metro Config: 最終配置', {
  hermesParser: config.transformer.hermesParser,
  enableBabelRCLookup: config.transformer.enableBabelRCLookup
});

module.exports = config;