/**
 * 🔢 遊戲常量定義
 * 
 * 定義遊戲中使用的各種常量值
 * 基於 docs/game_design.md 的設計規格
 */

import { BattleTag, CardRace, CardRarity, ElementTag, SpecialTag } from './index';

// ==================== 遊戲基礎常量 ====================

/**
 * 遊戲版本信息
 */
export const GAME_VERSION = {
  MAJOR: 1,
  MINOR: 0,
  PATCH: 0,
  BUILD: 1,
  get FULL() {
    return `${this.MAJOR}.${this.MINOR}.${this.PATCH}.${this.BUILD}`;
  }
} as const;

/**
 * 遊戲基礎配置
 */
export const GAME_CONFIG = {
  MAX_TEAM_SIZE: 9,                     // 最大隊伍大小 (3x3)

  MAX_CARD_LEVEL: 100,                  // 最大卡牌等級
  MAX_PLAYER_LEVEL: 999,                // 最大玩家等級
  ACTION_BAR_MAX: 100,                  // 行動條最大值
  CRITICAL_BASE_CHANCE: 0.05,           // 基礎暴擊率 (5%)
  CRITICAL_MULTIPLIER: 1.5,             // 暴擊倍率
  SPEED_TO_ACTION_RATIO: 0.1,           // 速度到行動條轉換比例
} as const;

// ==================== 卡牌相關常量 ====================

/**
 * 稀有度相關常量
 */
export const RARITY_CONFIG = {
  [CardRarity.COMMON]: {
    name: '普通',
    stars: 1,
    color: '#FFFFFF',
    probability: 0.60,
    maxLevel: 50,
    evolutionCost: 10,
  },
  [CardRarity.RARE]: {
    name: '稀有',
    stars: 2,
    color: '#4A90E2',
    probability: 0.25,
    maxLevel: 60,
    evolutionCost: 20,
  },
  [CardRarity.EPIC]: {
    name: '史詩',
    stars: 3,
    color: '#9013FE',
    probability: 0.12,
    maxLevel: 80,
    evolutionCost: 50,
  },
  [CardRarity.LEGENDARY]: {
    name: '傳說',
    stars: 4,
    color: '#FFD700',
    probability: 0.025,
    maxLevel: 90,
    evolutionCost: 100,
  },
  [CardRarity.MYTHIC]: {
    name: '神話',
    stars: 5,
    color: '#FF6B6B',
    probability: 0.005,
    maxLevel: 100,
    evolutionCost: 200,
  },
} as const;

/**
 * 種族相關常量
 */
export const RACE_CONFIG = {
  [CardRace.HUMAN]: {
    name: '人族',
    color: '#4A90E2',
    emoji: '👤',
    description: '平衡屬性，適應性強',
  },
  [CardRace.ELF]: {
    name: '精靈族',
    color: '#4CAF50',
    emoji: '🧝',
    description: '高速度魔攻，敏捷靈活',
  },
  [CardRace.ORC]: {
    name: '獸人族',
    color: '#FF9800',
    emoji: '👹',
    description: '高攻擊生命，勇猛無畏',
  },
  [CardRace.DRAGON]: {
    name: '龍族',
    color: '#F44336',
    emoji: '🐉',
    description: '極高屬性，威力強大',
  },
  [CardRace.ANGEL]: {
    name: '天使族',
    color: '#9C27B0',
    emoji: '👼',
    description: '治療輔助，神聖力量',
  },
  [CardRace.DEMON]: {
    name: '惡魔族',
    color: '#8B0000',
    emoji: '😈',
    description: '詛咒減益，黑暗力量',
  },
} as const;

/**
 * 標籤相關常量
 */
export const TAG_CONFIG = {
  BATTLE: {
    [BattleTag.WARRIOR]: { name: '戰士', color: '#FF9800', emoji: '⚔️' },
    [BattleTag.MAGE]: { name: '法師', color: '#9C27B0', emoji: '🔮' },
    [BattleTag.ARCHER]: { name: '射手', color: '#4CAF50', emoji: '🏹' },
    [BattleTag.TANK]: { name: '坦克', color: '#607D8B', emoji: '🛡️' },
    [BattleTag.HEALER]: { name: '治療', color: '#8BC34A', emoji: '💚' },
    [BattleTag.SUPPORT]: { name: '輔助', color: '#03A9F4', emoji: '🤝' },
  },
  ELEMENT: {
    [ElementTag.FIRE]: { name: '火', color: '#F44336', emoji: '🔥' },
    [ElementTag.WATER]: { name: '水', color: '#2196F3', emoji: '💧' },
    [ElementTag.EARTH]: { name: '土', color: '#8D6E63', emoji: '🌍' },
    [ElementTag.AIR]: { name: '風', color: '#00BCD4', emoji: '💨' },
    [ElementTag.LIGHT]: { name: '光', color: '#FFEB3B', emoji: '✨' },
    [ElementTag.DARK]: { name: '暗', color: '#424242', emoji: '🌑' },
  },
  SPECIAL: {
    [SpecialTag.ELITE]: { name: '精英', color: '#FF5722', emoji: '⭐' },
    [SpecialTag.BOSS]: { name: '首領', color: '#E91E63', emoji: '👑' },
    [SpecialTag.MINION]: { name: '小兵', color: '#9E9E9E', emoji: '👥' },
    [SpecialTag.FLYING]: { name: '飛行', color: '#00BCD4', emoji: '🦅' },
    [SpecialTag.UNDEAD]: { name: '亡靈', color: '#424242', emoji: '💀' },
    [SpecialTag.BEAST]: { name: '野獸', color: '#8BC34A', emoji: '🐺' },
  },
} as const;

// ==================== 戰鬥相關常量 ====================

/**
 * 戰鬥配置常量
 */
export const BATTLE_CONFIG = {
  REALTIME_TICK_MS: 100,                // 時間制更新刻(ms)
  AI_THINK_TIME: 1000,                  // AI思考時間(毫秒)
  ANIMATION_SPEED_MULTIPLIERS: [1, 2, 4], // 動畫速度倍率選項
  STATUS_EFFECT_MAX_STACKS: 5,          // 狀態效果最大疊加層數
  DAMAGE_VARIANCE: 0.1,                 // 傷害浮動範圍 (±10%)
  HEALING_VARIANCE: 0.05,               // 治療浮動範圍 (±5%)
  POSITION_COUNT: 9,                    // 戰場位置數量 (3x3)
} as const;

/**
 * 種族克制關係
 */
export const RACE_EFFECTIVENESS = undefined as unknown as never; // 時間制：不再使用種族克制

/**
 * 元素克制關係
 */
export const ELEMENT_EFFECTIVENESS = undefined as unknown as never; // 時間制：不再使用元素克制

/**
 * 克制效果倍率
 */
export const EFFECTIVENESS_MULTIPLIERS = undefined as unknown as never; // 時間制：不再使用克制倍率

// ==================== 抽卡相關常量 ====================

/**
 * 抽卡配置常量
 */
export const GACHA_CONFIG = {
  SINGLE_DRAW_COST: {
    DIAMOND: 100,                       // 鑽石單抽費用
    GOLD: 1000,                         // 金幣單抽費用
  },
  TEN_DRAW_COST: {
    DIAMOND: 900,                       // 鑽石十連費用 (9折)
    GOLD: 9000,                         // 金幣十連費用 (9折)
  },
  GUARANTEE_RULES: {
    RARE_GUARANTEE: 10,                 // 10抽保底稀有
    EPIC_GUARANTEE: 50,                 // 50抽保底史詩
    LEGENDARY_GUARANTEE: 200,           // 200抽保底傳說
  },
  FREE_DRAW_COOLDOWN: 24 * 60 * 60 * 1000, // 免費抽卡冷卻時間(24小時)
  RATE_UP_MULTIPLIER: 2.0,              // 提升機率倍數
} as const;

// ==================== UI相關常量 ====================

/**
 * UI尺寸常量
 */
export const UI_SIZES = {
  CARD: {
    NORMAL: { width: 120, height: 160 },
    MINI: { width: 80, height: 100 },
    LARGE: { width: 180, height: 240 },
  },
  TOUCH_TARGET: 48,                     // 最小觸控區域
  BUTTON_HEIGHT: 44,                    // 按鈕高度
  INPUT_HEIGHT: 40,                     // 輸入框高度
  HEADER_HEIGHT: 60,                    // 標題欄高度
  TAB_BAR_HEIGHT: 80,                   // 標籤欄高度
  FAB_SIZE: 60,                         // 懸浮按鈕大小
} as const;

/**
 * 動畫時長常量
 */
export const ANIMATION_DURATIONS = {
  FAST: 150,                            // 快速動畫
  NORMAL: 300,                          // 普通動畫
  SLOW: 500,                            // 慢速動畫
  CARD_FLIP: 1000,                      // 卡牌翻轉
  BATTLE_ACTION: 800,                   // 戰鬥行動
  STATUS_EFFECT: 600,                   // 狀態效果
  GACHA_REVEAL: 2000,                   // 抽卡揭示
} as const;

// ==================== 存儲相關常量 ====================

/**
 * 本地存儲鍵名
 */
export const STORAGE_KEYS = {
  PLAYER_DATA: 'playerData',
  GAME_SETTINGS: 'gameSettings',
  CARD_COLLECTION: 'cardCollection',
  TEAM_CONFIGS: 'teamConfigs',
  GACHA_HISTORY: 'gachaHistory',
  BATTLE_HISTORY: 'battleHistory',
  TUTORIAL_PROGRESS: 'tutorialProgress',
  LAST_LOGIN: 'lastLogin',
} as const;

/**
 * 配置文件路徑
 */
export const CONFIG_PATHS = {
  CARDS: 'config/CardConfig.csv',
  SKILLS: 'config/SkillConfig.csv',
  STAGES: 'config/StageConfig.csv',
  ENEMY_DECKS: 'config/EnemyDeckConfig.csv',
  DROPS: 'config/DropConfig.csv',
  GACHA: 'config/GachaConfig.csv',
  AI_BEHAVIORS: 'config/AIBehaviorConfig.csv',
} as const;

// ==================== 錯誤代碼常量 ====================

/**
 * 錯誤代碼定義
 */
export const ERROR_CODES = {
  // 通用錯誤
  UNKNOWN_ERROR: 'E0001',
  INVALID_PARAMETER: 'E0002',
  NETWORK_ERROR: 'E0003',
  
  // 配置錯誤
  CONFIG_LOAD_FAILED: 'E1001',
  CONFIG_PARSE_FAILED: 'E1002',
  CONFIG_VALIDATION_FAILED: 'E1003',
  
  // 卡牌錯誤
  CARD_NOT_FOUND: 'E2001',
  CARD_LEVEL_MAX: 'E2002',
  CARD_EVOLUTION_FAILED: 'E2003',
  
  // 戰鬥錯誤
  BATTLE_INIT_FAILED: 'E3001',
  BATTLE_INVALID_ACTION: 'E3002',
  BATTLE_TIMEOUT: 'E3003',
  
  // 抽卡錯誤
  GACHA_INSUFFICIENT_CURRENCY: 'E4001',
  GACHA_POOL_INACTIVE: 'E4002',
  GACHA_DRAW_FAILED: 'E4003',
  
  // 存儲錯誤
  STORAGE_SAVE_FAILED: 'E5001',
  STORAGE_LOAD_FAILED: 'E5002',
  STORAGE_CORRUPTED: 'E5003',
} as const;

// ==================== 調試相關常量 ====================

/**
 * 調試配置
 */
export const DEBUG_CONFIG = {
  ENABLE_LOGGING: __DEV__,              // 是否啟用日誌
  LOG_LEVEL: __DEV__ ? 'debug' : 'error', // 日誌級別
  ENABLE_PERFORMANCE_MONITORING: __DEV__, // 是否啟用性能監控
  SHOW_DEBUG_INFO: __DEV__,             // 是否顯示調試信息
  MOCK_DATA: __DEV__,                   // 是否使用模擬數據
} as const;
