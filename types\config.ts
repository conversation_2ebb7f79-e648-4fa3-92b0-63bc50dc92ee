/**
 * 🔧 配置系統類型定義
 *
 * 定義所有CSV配置文件的數據結構
 * 對應 docs/game_design.md 中的配置系統設計
 */

import {
  AIBehaviorType,
  CardRace,
  CardRarity,
  CombinedTag,
  SkillEffectType,
  SkillTargetType
} from './index';


// ==================== CSV配置接口 ====================

/**
 * 卡牌配置數據 (CardConfig.csv)
 */
export interface CardConfig {
  id: string;                           // 卡牌配置ID
  name: string;                         // 卡牌名稱
  race: CardRace[];                     // 種族 (可多選)
  rarity: CardRarity;                   // 稀有度
  baseAttack: number;                   // 基礎物理攻擊
  baseMagicAttack: number;              // 基礎魔法攻擊
  baseDefense: number;                  // 基礎防禦
  baseCritRate: number;                 // 基礎暴擊率 (0-1)
  baseHealth: number;                   // 基礎生命值
  baseSpeed: number;                    // 基礎速度
  tags: CombinedTag[];                  // 合併後的通用標籤
  skillIds: string[];                   // 技能ID列表
  imageUrl: string;                     // 卡牌圖片路徑
  description: string;                  // 卡牌描述
  attackGrowth: number;                 // 物攻成長
  magicAttackGrowth: number;            // 魔攻成長
  defenseGrowth: number;                // 防禦成長
  critRateGrowth: number;               // 暴擊率成長
  healthGrowth: number;                 // 生命值成長
  speedGrowth: number;                  // 速度成長
  maxLevel: number;                     // 最大等級
  evolutionCardId?: string;             // 進化後卡牌ID
  evolutionRequiredLevel: number;       // 進化所需等級
  evolutionRequiredCards: number;       // 進化所需卡牌數量
}

/**
 * 技能配置數據 (SkillConfig.csv)
 */
export interface SkillConfig {
  id: string;                           // 技能ID
  name: string;                         // 技能名稱
  description: string;                  // 技能描述
  targetType: SkillTargetType;          // 目標類型
  effectType: SkillEffectType;          // 效果類型
  baseValue: number;                    // 基礎數值
  valueGrowth: number;                  // 數值成長
  cooldown: number;                     // 冷卻時間
  // manaCost 移除：所有技能不消耗魔力
  animationId: string;                  // 動畫ID
  soundId: string;                      // 音效ID
  particleEffectId: string;             // 粒子效果ID
  statusEffectId?: string;              // 狀態效果ID
  statusEffectDuration: number;         // 狀態效果持續時間
  isPassive: boolean;                   // 是否被動技能
  triggerCondition?: string;            // 觸發條件
  priority: number;                     // 優先級
}

/**
 * 關卡配置數據 (StageConfig.csv)
 */
export interface StageLoopPartConfig {
  monsterPoolDeckId: string;            // 循環戰鬥的怪物池牌組ID
  minEnemiesPerBattle: number;          // 每場戰鬥最少怪物數
  maxEnemiesPerBattle: number;          // 每場戰鬥最多怪物數
  dropPoolId: string;                   // 掉落池(循環部分)
}

export interface StageBossPartConfig {
  bossDeckId: string;                   // Boss固定牌組ID
  dropPoolId: string;                   // 掉落池(Boss部分)
}

export interface StageConfig {
  id: string;                           // 關卡ID
  name: string;                         // 關卡名稱
  description: string;                  // 關卡描述
  stageNumber: number;                  // 關卡編號
  backgroundImageUrl: string;           // 背景圖片
  musicId: string;                      // 背景音樂ID
  previousStageId?: string;             // 前置關卡ID
  rewardGold: number;                   // 金幣獎勵
  firstClearRewardId?: string;          // 首次通關獎勵ID
  recommendedPower: number;             // 推薦戰力
  parts: {
    loop: StageLoopPartConfig;          // 第一部分：可循環隨機遭遇
    boss: StageBossPartConfig;          // 第二部分：Boss固定遭遇
  };
}

/**
 * 敵方牌組配置 (EnemyDeckConfig.csv)
 */
export interface EnemyDeckConfig {
  id: string;                           // 牌組ID
  name: string;                         // 牌組名稱
  cardIds: string[];                    // 卡牌ID列表
  cardLevels: number[] | string[];      // 卡牌等級列表（CSV可為數字或字串）
  aiBehaviorType: AIBehaviorType;       // AI行為模式
  totalPower: number;                   // 總戰力
  formation: string;                    // 陣型配置 (位置編號)
  specialRules?: string;                // 特殊規則
}

/**
 * 玩家初始隊伍配置 (PlayerTeamConfig.csv)
 */
export interface PlayerTeamConfig {
  id: string;                           // 隊伍ID
  name: string;                         // 隊伍名稱
  cardIds: string[];                    // 卡牌ID列表
  cardLevels: number[] | string[];      // 卡牌等級列表
  formation?: string;                   // 陣型配置
  description?: string;                 // 說明
}

/**
 * 掉落配置數據 (DropConfig.csv)
 */
export interface DropConfig {
  id: string;                           // 掉落池ID
  name: string;                         // 掉落池名稱
  dropItems: DropItem[];                // 掉落物品列表
  guaranteedDrops: DropItem[];          // 保底掉落
  maxDropCount: number;                 // 最大掉落數量
  minDropCount: number;                 // 最小掉落數量
}

/**
 * 掉落物品接口
 */
export interface DropItem {
  type: 'card' | 'gold' | 'diamond' | 'experience';
  itemId?: string;                      // 物品ID (卡牌ID等)
  value: number;                        // 數值 (金幣數量等)
  probability: number;                  // 掉落機率 (0-1)
  rarity?: CardRarity;                  // 稀有度限制
}

/**
 * 抽卡池配置 (GachaConfig.csv)
 */
export interface GachaConfig {
  id: string;                           // 抽卡池ID
  name: string;                         // 抽卡池名稱
  description: string;                  // 描述
  isActive: boolean;                    // 是否啟用
  startTime?: Date;                     // 開始時間
  endTime?: Date;                       // 結束時間
  singleDrawCost: number;               // 單抽費用
  tenDrawCost: number;                  // 十連費用
  currencyType: 'gold' | 'diamond';    // 貨幣類型
  cardPool: GachaCardPool[];            // 卡牌池
  guaranteeRules: GachaGuaranteeRule[]; // 保底規則
  bannerImageUrl: string;               // 橫幅圖片
  featuredCardIds: string[];            // 精選卡牌ID
}

/**
 * 抽卡池卡牌配置
 */
export interface GachaCardPool {
  cardId: string;                       // 卡牌ID
  rarity: CardRarity;                   // 稀有度
  probability: number;                  // 抽取機率
  isRateUp: boolean;                    // 是否提升機率
  rateUpMultiplier: number;             // 機率提升倍數
}

/**
 * 抽卡保底規則
 */
export interface GachaGuaranteeRule {
  type: 'pity' | 'guarantee';           // 保底類型
  rarity: CardRarity;                   // 目標稀有度
  drawCount: number;                    // 抽取次數
  resetOnGet: boolean;                  // 獲得後是否重置
}

/**
 * AI行為配置 (AIBehaviorConfig.csv)
 */
export interface AIBehaviorConfig {
  id: string;                           // AI配置ID
  name: string;                         // AI名稱
  behaviorType: AIBehaviorType;         // 行為類型
  targetPriority: AITargetPriority[];   // 目標優先級
  skillUsePriority: AISkillPriority[];  // 技能使用優先級
  defensiveThreshold: number;           // 防禦閾值 (生命值百分比)
  aggressiveThreshold: number;          // 攻擊閾值 (生命值百分比)
  retreatThreshold: number;             // 撤退閾值 (生命值百分比)
  randomnessFactor: number;             // 隨機性因子 (0-1)
}

/**
 * AI目標優先級
 */
export interface AITargetPriority {
  condition: string;                    // 條件描述
  targetType: 'lowest_hp' | 'highest_attack' | 'random' | 'nearest' | 'farthest';
  priority: number;                     // 優先級 (1-10)
}

/**
 * AI技能優先級
 */
export interface AISkillPriority {
  skillType: SkillEffectType;           // 技能類型
  condition: string;                    // 使用條件
  priority: number;                     // 優先級 (1-10)
  cooldownRespect: boolean;             // 是否考慮冷卻時間
}

// ==================== 配置管理接口 ====================

/**
 * 配置管理器接口
 */
export interface ConfigManager {
  cards: Map<string, CardConfig>;       // 卡牌配置
  skills: Map<string, SkillConfig>;     // 技能配置
  stages: Map<string, StageConfig>;     // 關卡配置
  enemyDecks: Map<string, EnemyDeckConfig>; // 敵方牌組配置
  drops: Map<string, DropConfig>;       // 掉落配置
  gacha: Map<string, GachaConfig>;      // 抽卡配置
  aiBehaviors: Map<string, AIBehaviorConfig>; // AI行為配置
  playerTeams: Map<string, PlayerTeamConfig>; // 玩家隊伍配置

  // 配置載入方法
  loadConfigs(): Promise<void>;
  getCardConfig(id: string): CardConfig | undefined;
  getSkillConfig(id: string): SkillConfig | undefined;
  getStageConfig(id: string): StageConfig | undefined;
  getEnemyDeckConfig(id: string): EnemyDeckConfig | undefined;
  getDropConfig(id: string): DropConfig | undefined;
  getGachaConfig(id: string): GachaConfig | undefined;
  getAIBehaviorConfig(id: string): AIBehaviorConfig | undefined;
  getPlayerTeamConfig(id: string): PlayerTeamConfig | undefined;
}

/**
 * CSV解析結果接口
 */
export interface CSVParseResult<T> {
  success: boolean;                     // 是否成功
  data: T[];                            // 解析後的數據
  errors: string[];                     // 錯誤信息
  warnings: string[];                   // 警告信息
}

/**
 * 配置驗證結果接口
 */
export interface ConfigValidationResult {
  isValid: boolean;                     // 是否有效
  errors: ConfigValidationError[];      // 錯誤列表
  warnings: ConfigValidationWarning[];  // 警告列表
}

/**
 * 配置驗證錯誤
 */
export interface ConfigValidationError {
  configType: string;                   // 配置類型
  configId: string;                     // 配置ID
  field: string;                        // 錯誤字段
  message: string;                      // 錯誤信息
  severity: 'error' | 'warning';        // 嚴重程度
}

/**
 * 配置驗證警告
 */
export interface ConfigValidationWarning {
  configType: string;                   // 配置類型
  configId: string;                     // 配置ID
  field: string;                        // 警告字段
  message: string;                      // 警告信息
  suggestion?: string;                  // 建議
}
