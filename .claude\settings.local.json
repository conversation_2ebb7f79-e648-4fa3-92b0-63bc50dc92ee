{"permissions": {"allow": ["Bash(npm install:*)", "Bash(npx create-expo-app:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(rmdir:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(npx expo install:*)", "<PERSON><PERSON>(curl:*)", "Bash(expo --version)", "Bash(npx expo:*)", "Bash(timeout 30 npx expo start --web --non-interactive)", "Bash(CI=1 timeout 20 npx expo export:web --dev)", "Bash(export CI=1)", "Bash(timeout 20 npx expo export:web --dev)", "<PERSON><PERSON>(pkill:*)"], "deny": []}}