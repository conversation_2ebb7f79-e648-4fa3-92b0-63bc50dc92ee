import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { VintagePaperTheme } from '../styles/VintagePaperTheme';

interface SectionHeaderProps {
  team: 'player' | 'enemy';
  teamName: string;
  totalHp: number;
  icon?: string;
}

const { height } = Dimensions.get('window');

const SectionHeader: React.FC<SectionHeaderProps> = ({ 
  team, 
  teamName, 
  totalHp, 
  icon 
}) => {
  const theme = VintagePaperTheme;
  const fontSize = theme.typography.sectionTitle(height);
  
  const teamIcon = icon || (team === 'enemy' ? '🔴' : '🔵');
  const teamColor = team === 'enemy' ? theme.colors.bad : theme.colors.good;
  
  return (
    <View style={styles.sectionHeader}>
      <Text style={[styles.teamText, { fontSize, color: teamColor }]}>
        {teamIcon} {teamName}
      </Text>
      <Text style={[styles.hpText, { fontSize }]}>
        ❤️ {totalHp}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: VintagePaperTheme.spacing.md,
    paddingHorizontal: VintagePaperTheme.spacing.xs,
  },
  
  teamText: {
    fontWeight: 'bold',
    transform: [{ rotate: '-1deg' }],
  },
  
  hpText: {
    fontWeight: 'bold',
    color: VintagePaperTheme.colors.ink,
    transform: [{ rotate: '1deg' }],
  },
});

export default SectionHeader;
