import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
    useWindowDimensions,
    ActivityIndicator,
} from 'react-native';
import { useConfig } from '../contexts/ConfigContext';
import { CardConfig, PlayerTeamConfig, EnemyDeckConfig } from '../types/config';

// 添加調試日誌
console.log('BattleScreen.tsx: 文件已載入');

// 類型定義 (與之前相同，但現在部分屬性從 CardConfig 衍生)
interface Unit {
    id: string;
    team: 'player' | 'enemy';
    name: string;
    level: number;
    icon: string;
    race: string;
    rarity: number;
    stats: {
        hp: number;
        maxHp: number;
        mp: number;
        maxMp: number;
        atk: number;
        spd: number;
    };
    tags: string[];
    status: Status[];
    skills: string[];
    alive: boolean;
    isEmpty?: boolean;
}

interface Status {
    statusId: string;
    stacks: number;
    duration: number;
}

interface Skill {
    id: string;
    name: string;
    type: 'attack' | 'buff';
    power: number;
    mpCost: number;
    target: string;
    cooldown: number;
    currentCooldown: number;
    appliesStatus: string | null;
}

const { height } = Dimensions.get('window');

// 輔助函數：從配置創建戰鬥單位
const createUnitFromConfig = (
    cardConfig: CardConfig,
    level: number,
    team: 'player' | 'enemy',
    instanceId: string,
): Unit => {
    // 在這裡可以加入等級對屬性的影響計算
    const atk = cardConfig.baseAttack + cardConfig.attackGrowth * level;
    const maxHp = cardConfig.baseHealth + cardConfig.healthGrowth * level;
    const spd = cardConfig.baseSpeed + cardConfig.speedGrowth * level;

    return {
        id: instanceId,
        team,
        name: cardConfig.name,
        level,
        // TODO: 根據 race 或其他屬性決定圖標
        icon: team === 'player' ? '🤺' : '💀',
        race: cardConfig.race.join(', '),
        rarity: cardConfig.rarity,
        stats: {
            hp: maxHp,
            maxHp,
            mp: 100, // 暫定
            maxMp: 100, // 暫定
            atk,
            spd,
        },
        tags: cardConfig.tags,
        status: [],
        skills: cardConfig.skillIds,
        alive: true,
    };
};

const BattleScreen: React.FC = () => {
    const { configManager, isLoaded, isLoading } = useConfig();
    const { width: screenWidth, height: screenHeight } = useWindowDimensions();
    const [playerTeam, setPlayerTeam] = useState<Unit[]>([]);
    const [enemyTeam, setEnemyTeam] = useState<Unit[]>([]);

    // 扣除導航欄高度（約6%）
    const availableHeight = screenHeight * 0.94;

    useEffect(() => {
        if (isLoaded) {
            console.log('BattleScreen: 配置已加載，正在準備隊伍...');
            prepareTeams();
        }
    }, [isLoaded]);

    const prepareTeams = () => {
        // 獲取第一個關卡配置
        const stage = configManager.getStageConfig('STAGE_001');
        if (!stage) {
            console.error('找不到關卡配置: STAGE_001');
            return;
        }

        // 準備玩家隊伍
        const playerTeamConfig = configManager.getPlayerTeamConfig('PLAYER_TEAM_001');
        if (playerTeamConfig) {
            console.log('玩家隊伍配置:', playerTeamConfig);
            console.log('cardIds 類型:', typeof playerTeamConfig.cardIds, playerTeamConfig.cardIds);
            console.log('cardLevels 類型:', typeof playerTeamConfig.cardLevels, playerTeamConfig.cardLevels);

            // 確保 cardIds 是數組
            const cardIds = Array.isArray(playerTeamConfig.cardIds)
                ? playerTeamConfig.cardIds
                : [playerTeamConfig.cardIds];

            const newPlayerTeam = cardIds.map((cardId, index) => {
                console.log(`🔍 查找卡牌配置: ${cardId}`);
                const cardConfig = configManager.getCardConfig(cardId);
                console.log(`🔍 找到卡牌配置:`, cardConfig);

                // 確保 cardLevels 是數組並獲取對應等級
                const cardLevels = Array.isArray(playerTeamConfig.cardLevels)
                    ? playerTeamConfig.cardLevels
                    : [playerTeamConfig.cardLevels];
                const cardLevel = Number(cardLevels[index] || 1);
                if (cardConfig) {
                    const unit = createUnitFromConfig(cardConfig, cardLevel, 'player', `${cardId}_p${index}`);
                    console.log(`✅ 創建玩家單位:`, unit);
                    return unit;
                } else {
                    console.error(`❌ 找不到卡牌配置: ${cardId}`);
                }
                return null;
            }).filter(unit => unit !== null) as Unit[];
            setPlayerTeam(newPlayerTeam);
            console.log('玩家隊伍已設定:', newPlayerTeam);
        }

        // 準備敵人隊伍 (使用Boss隊伍)
        const enemyDeckId = stage.parts.boss.bossDeckId;
        const enemyDeck = configManager.getEnemyDeckConfig(enemyDeckId);
        if (enemyDeck) {
            console.log('敵人牌組配置:', enemyDeck);
            console.log('敵人 cardIds 類型:', typeof enemyDeck.cardIds, enemyDeck.cardIds);
            console.log('敵人 cardLevels 類型:', typeof enemyDeck.cardLevels, enemyDeck.cardLevels);

            // 確保 cardIds 是數組
            const cardIds = Array.isArray(enemyDeck.cardIds)
                ? enemyDeck.cardIds
                : [enemyDeck.cardIds];

            const newEnemyTeam = cardIds.map((cardId, index) => {
                console.log(`🔍 查找敵人卡牌配置: ${cardId}`);
                const cardConfig = configManager.getCardConfig(cardId);
                console.log(`🔍 找到敵人卡牌配置:`, cardConfig);

                // 確保 cardLevels 是數組並獲取對應等級
                const cardLevels = Array.isArray(enemyDeck.cardLevels)
                    ? enemyDeck.cardLevels
                    : [enemyDeck.cardLevels];
                const cardLevel = Number(cardLevels[index] || 1);
                if (cardConfig) {
                    const unit = createUnitFromConfig(cardConfig, cardLevel, 'enemy', `${cardId}_e${index}`);
                    console.log(`✅ 創建敵人單位:`, unit);
                    return unit;
                } else {
                    console.error(`❌ 找不到敵人卡牌配置: ${cardId}`);
                }
                return null;
            }).filter(unit => unit !== null) as Unit[];
            setEnemyTeam(newEnemyTeam);
            console.log('敵人隊伍已設定:', newEnemyTeam);
        }
    };

    // 版面尺寸（6% 標題 + 47% 敵方 + 47% 我方）
    const HEADER_RATIO = 0.06;
    const SECTION_RATIO = 0.47;
    const headerHeight = availableHeight * HEADER_RATIO;
    const sectionHeight = availableHeight * SECTION_RATIO;

    // 依據區塊大小計算 3x3 卡片尺寸（維持 3:4 比例）
    const getCardSize = () => {

        const rows = 3;
        const horizontalPadding = 12; // 每側 6
        const verticalPadding = 24;   // 上下各 12
        const marginBottom = 6; // gridCell marginBottom

        const availableWidth = screenWidth - horizontalPadding;
        const sectionAvailableHeight = sectionHeight - verticalPadding - (rows - 1) * marginBottom;

        // 每列寬度為 32% 的容器寬度（確保3列能放下）
        const cellWidth = availableWidth * 0.32;
        const maxHeightPerRow = Math.floor(sectionAvailableHeight / rows);

        // 保持 3:4 比例，但不超過可用空間
        const cardWidth = Math.min(cellWidth - 4, Math.floor(maxHeightPerRow * 0.75)); // 減去邊距
        const cardHeight = Math.min(maxHeightPerRow - 4, Math.floor(cardWidth * 4 / 3));

        return {
            cardWidth: Math.max(110, Math.min(cardWidth, 200)),
            cardHeight: Math.max(140, Math.min(cardHeight, 260))
        };
    };
    const percent = (part: number, whole: number) =>
        Math.max(0, Math.min(100, Math.round((part / whole) * 100)));



    // 卡片組件
    const BattleCard = ({ unit, cardWidth, cardHeight }: { unit: Unit; cardWidth: number; cardHeight: number }) => {
        // 動態字體大小（跟隨卡片高度縮放，更保守的計算）
        const base = Math.max(10, Math.min(14, Math.round(cardHeight * 0.08)));
        const iconSize = Math.max(16, Math.min(24, Math.round(cardHeight * 0.15)));

        // 如果是空佔位符，顯示空卡片
        if (unit.isEmpty) {
            return (
                <View style={[styles.card, styles.emptyCard, { width: cardWidth, height: cardHeight }]}>
                    <View style={styles.emptyCardContent}>
                        <Text style={[styles.emptyCardText, { fontSize: base * 0.9 }]}>空</Text>
                    </View>
                </View>
            );
        }

        return (
            <View
                style={[
                    styles.card,
                    { width: cardWidth, height: cardHeight },
                    unit.team === 'enemy' ? styles.enemyCard : styles.playerCard,
                    !unit.alive && styles.koCard,
                ]}>
                <View style={[styles.cardLevel, { width: base * 1.4, height: base * 1.4 }] }>
                    <Text style={[styles.cardLevelText, { fontSize: base * 0.7 }]}>{unit.level}</Text>
                </View>
                <Text style={[styles.cardIcon, { fontSize: iconSize, marginTop: base * 0.5 }]}>{unit.icon}</Text>
                <Text
                    style={[styles.cardName, { fontSize: base * 0.9, maxWidth: cardWidth - 8 }]}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                >
                    {unit.name}
                </Text>
                <View style={styles.cardStats}>
                    <Text style={[styles.cardAtk, { fontSize: base * 0.9 }]}>⚔️{unit.stats.atk}</Text>
                    <Text style={[styles.cardSpd, { fontSize: base * 0.9 }]}>⚡{unit.stats.spd}</Text>
                </View>
                <View style={[styles.hpBar, { height: Math.max(4, base * 0.35) }]}>
                    <View
                        style={[
                            styles.hpFill,
                            { width: `${percent(unit.stats.hp, unit.stats.maxHp)}%` },
                            unit.team === 'player' ? styles.playerHpFill : styles.enemyHpFill,
                        ]}
                    />
                </View>
                <View style={styles.statusRow}>
                    {unit.status.map((status, index) => (
                        <Text key={index} style={[styles.statusIcon, { fontSize: base * 0.8 }]}>
                            {status.statusId === 'haste' ? '⚡' : '🛡️'}
                        </Text>
                    ))}
                </View>
                {!unit.alive && <Text style={[styles.koText, { fontSize: base * 0.9 }]}>KO</Text>}
            </View>
        );
    };



    // 渲染玩家單位
    const renderPlayerUnits = () => {
        const placeholders = Array.from({ length: 9 }, (_, index) => {
            const unit = playerTeam[index];
            return unit || {
                id: `p_placeholder_${index}`,
                team: 'player', name: '', level: 0, icon: '', race: '', rarity: 0,
                stats: { hp: 0, maxHp: 0, mp: 0, maxMp: 0, atk: 0, spd: 0 },
                tags: [], status: [], skills: [], alive: false, isEmpty: true,
            };
        });

        const { cardWidth, cardHeight } = getCardSize();

        return (
            <View style={[styles.playerSection, { height: sectionHeight }]}>
                <View style={styles.cardsGrid}>
                    {placeholders.map((unit) => (
                        <View key={unit.id} style={styles.gridCell}>
                            <BattleCard unit={unit} cardWidth={cardWidth} cardHeight={cardHeight} />
                        </View>
                    ))}
                </View>
            </View>
        );
    };

    // 渲染敵人單位
    const renderEnemyUnits = () => {
        const placeholders = Array.from({ length: 9 }, (_, index) => {
            const unit = enemyTeam[index];
            return unit || {
                id: `e_placeholder_${index}`,
                team: 'enemy', name: '', level: 0, icon: '', race: '', rarity: 0,
                stats: { hp: 0, maxHp: 0, mp: 0, maxMp: 0, atk: 0, spd: 0 },
                tags: [], status: [], skills: [], alive: false, isEmpty: true,
            };
        });

        const { cardWidth, cardHeight } = getCardSize();

        return (
            <View style={[styles.enemySection, { height: sectionHeight }]}>
                <View style={styles.cardsGrid}>
                    {placeholders.map((unit) => (
                        <View key={unit.id} style={styles.gridCell}>
                            <BattleCard unit={unit} cardWidth={cardWidth} cardHeight={cardHeight} />
                        </View>
                    ))}
                </View>
            </View>
        );
    };

    if (isLoading) {
        return (
            <View style={[styles.container, styles.centerContent]}>
                <ActivityIndicator size="large" color="#8b4513" />
                <Text style={styles.loadingText}>正在載入戰鬥數據...</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            {/* 標題區（6%） */}
            <View style={[styles.headerArea, { height: headerHeight }]}>
                <Text style={styles.stageTitle}>⚔️ 第 1-1 關：森林小徑</Text>
                <TouchableOpacity style={styles.topControlButton}>
                    <Text style={styles.controlButtonText}>☰</Text>
                </TouchableOpacity>
            </View>

            {/* 敵方區（47%） */}
            {renderEnemyUnits()}

            {/* 我方區（47%） */}
            {renderPlayerUnits()}
        </View>
    );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f3e9',
    width: '100%',
    height: '100%',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#8b4513',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'rgba(139, 69, 19, 0.2)',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(139, 69, 19, 0.3)',
  },
  headerArea: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 6,
    paddingBottom: 6,
    backgroundColor: 'rgba(139, 69, 19, 0.08)',
    position: 'relative',
    minHeight: Math.max(50, height * 0.06),
  },
  stageTitle: {
    fontSize: Math.max(22, Math.round(height * 0.028)),
    fontWeight: 'bold',
    color: '#8b4513',
    textAlign: 'center',
    marginTop: 2,
    marginBottom: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 18,
    paddingVertical: 8,
    borderRadius: 16,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  stageTitleInline: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#8b4513',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  headerControls: {
    flexDirection: 'row',
    gap: 10,
  },
  controlButton: {
    width: 36,
    height: 36,
    backgroundColor: '#f4a261',
    borderWidth: 2,
    borderColor: '#8b4513',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    fontSize: Math.max(18, height * 0.022),
    color: '#8b4513',
    fontWeight: 'bold',
  },
  enemySection: {
    backgroundColor: 'rgba(220, 38, 38, 0.08)',
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(139, 69, 19, 0.3)',
    position: 'relative',
    justifyContent: 'center',
  },
  playerSection: {
    backgroundColor: 'rgba(34, 139, 34, 0.08)',
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(139, 69, 19, 0.3)',
    justifyContent: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  enemyHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#dc2626',
    transform: [{ rotate: '-1deg' }],
  },
  playerHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#228b22',
    transform: [{ rotate: '1deg' }],
  },
  cardsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    flexWrap: 'wrap',
    paddingHorizontal: 6,
    paddingVertical: 6,
    alignContent: 'flex-start',
  },
  gridCell: {
    width: '32%',
    marginBottom: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    backgroundColor: '#f9f7f4',
    borderWidth: 3,
    borderColor: '#8b4513',
    borderRadius: 10,
    padding: 8,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 6,
  },
  enemyCard: {
    borderColor: 'rgba(220, 38, 38, 0.85)',
  },
  playerCard: {
    borderColor: 'rgba(34, 139, 34, 0.85)',
  },
  koCard: {
    opacity: 0.45,
  },
  cardLevel: {
    position: 'absolute',
    top: -4,
    left: -4,
    backgroundColor: '#f4a261',
    color: '#fff',
    borderWidth: 1,
    borderColor: '#8b4513',
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 3,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  cardLevelText: {
    fontWeight: 'bold',
  },
  cardIcon: {
    zIndex: 2,
  },
  cardName: {
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 2,
    color: '#8b4513',
    zIndex: 2,
    textDecorationLine: 'underline',
    textDecorationColor: 'rgba(139, 69, 19, 0.3)',
  },
  cardStats: {
    flexDirection: 'row',
    gap: 6,
    fontWeight: 'bold',
    color: '#8b4513',
    zIndex: 2,
  },
  cardAtk: {
    color: '#d2691e',
  },
  cardSpd: {
    color: '#228b22',
  },
  hpBar: {
    width: '92%',
    backgroundColor: 'rgba(139, 69, 19, 0.2)',
    borderRadius: 3,
    overflow: 'hidden',
    marginTop: 4,
    borderWidth: 1,
    borderColor: '#8b4513',
    zIndex: 2,
  },
  hpFill: {
    height: '100%',
    backgroundColor: '#dc2626',
  },
  playerHpFill: {
    backgroundColor: '#228b22',
  },
  enemyHpFill: {
    backgroundColor: '#dc2626',
  },
  statusRow: {
    flexDirection: 'row',
    gap: 4,
    marginTop: 2,
    zIndex: 2,
  },
  statusIcon: {
    lineHeight: 12,
  },
  koText: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: '#8b0000',
    color: '#fff',
    borderWidth: 2,
    borderColor: '#3c2415',
    borderRadius: 6,
    paddingVertical: 0,
    paddingHorizontal: 4,
    zIndex: 4,
    transform: [{ rotate: '-10deg' }],
  },
  actionSection: {
    backgroundColor: 'transparent',
    paddingVertical: 6,
    paddingHorizontal: 10,
    alignItems: 'center',
    position: 'relative',
  },
  nextActionText: {
    fontSize: 14,
    marginBottom: 8,
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#8b4513',
    transform: [{ rotate: '-1deg' }],
  },
  turnQueue: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    position: 'relative',
    zIndex: 2,
  },
  turnIndicator: {
    width: 32,
    height: 32,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#8b4513',
  },
  playerIndicator: {
    backgroundColor: '#228b22',
    transform: [{ rotate: '-3deg' }],
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  enemyIndicator: {
    backgroundColor: '#dc2626',
    transform: [{ rotate: '2deg' }],
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  activeIndicator: {
    borderWidth: 3,
    borderColor: '#f4a261',
  },
  turnIndicatorText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
  },
  actionBar: {
    padding: 8,
    backgroundColor: 'rgba(139, 69, 19, 0.15)',
    borderTopWidth: 2,
    borderTopColor: 'rgba(139, 69, 19, 0.4)',
  },
  actionMpText: {
    fontWeight: 'bold',
    marginBottom: 6,
    color: '#3c2415',
  },
  skillBar: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  skillButton: {
    minHeight: 44,
    padding: 8,
    minWidth: 96,
    backgroundColor: '#f9f7f4',
    borderWidth: 2,
    borderColor: '#8b4513',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  skillName: {
    fontWeight: 'bold',
    color: '#3c2415',
  },
  skillCost: {
    fontSize: 11,
    opacity: 0.8,
    color: '#3c2415',
  },
  skillCooldown: {
    fontSize: 11,
    opacity: 0.8,
    color: '#3c2415',
  },
  logContainer: {
    margin: 8,
  },
  logToggle: {
    padding: 8,
    backgroundColor: 'rgba(139, 69, 19, 0.1)',
    borderRadius: 8,
    alignItems: 'center',
  },
  logToggleText: {
    fontWeight: 'bold',
    color: '#8b4513',
  },
  logList: {
    marginTop: 8,
    maxHeight: 150,
  },
  logItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderWidth: 1,
    borderColor: '#8b4513',
    borderStyle: 'dashed',
    borderRadius: 6,
    padding: 6,
    marginBottom: 6,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.25,
    shadowRadius: 0,
    elevation: 1,
  },
  logItemText: {
    color: '#3c2415',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(139, 69, 19, 0.3)',
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 3,
    borderTopColor: 'rgba(139, 69, 19, 0.5)',
  },
  bottomButton: {
    flex: 1,
    padding: 10,
    backgroundColor: '#f4a261',
    borderWidth: 2,
    borderColor: '#8b4513',
    borderRadius: 8,
    marginHorizontal: 5,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  pauseButton: {
    backgroundColor: 'rgba(220, 38, 38, 0.85)',
  },
  bottomButtonText: {
    color: '#8b4513',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyCard: {
    backgroundColor: '#f5f5f5',
    borderColor: '#ccc',
    borderStyle: 'dashed',
    opacity: 0.6,
  },
  emptyCardContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCardText: {
    color: '#999',
    fontSize: 12,
    fontWeight: 'bold',
  },
  topControlButton: {
    position: 'absolute',
    top: 8,
    right: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
    minWidth: Math.max(48, height * 0.06),
    minHeight: Math.max(48, height * 0.06),
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#8b4513',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  stageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    backgroundColor: 'transparent',
  },
  actionLine: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: 'rgba(139, 69, 19, 0.4)',
    zIndex: 1,
  },
});

// 添加調試日誌
console.log('BattleScreen.tsx: 導出 BattleScreen 組件');
export default BattleScreen;
