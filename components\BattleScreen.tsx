import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
    useWindowDimensions,
    ActivityIndicator,
} from 'react-native';
import { useConfig } from '../contexts/ConfigContext';
import { CardConfig, PlayerTeamConfig, EnemyDeckConfig } from '../types/config';
import { VintagePaperTheme, calculateCardSize } from '../styles/VintagePaperTheme';
import VintagePaperCard from './VintagePaperCard';
import ActionDivider from './ActionDivider';
import SectionHeader from './SectionHeader';

// 添加調試日誌
console.log('BattleScreen.tsx: 文件已載入');

// 類型定義 (與之前相同，但現在部分屬性從 CardConfig 衍生)
interface Unit {
    id: string;
    team: 'player' | 'enemy';
    name: string;
    level: number;
    icon: string;
    race: string;
    rarity: number;
    stats: {
        hp: number;
        maxHp: number;
        mp: number;
        maxMp: number;
        atk: number;
        spd: number;
    };
    tags: string[];
    status: Status[];
    skills: string[];
    alive: boolean;
    isEmpty?: boolean;
}

interface Status {
    statusId: string;
    stacks: number;
    duration: number;
}

interface Skill {
    id: string;
    name: string;
    type: 'attack' | 'buff';
    power: number;
    mpCost: number;
    target: string;
    cooldown: number;
    currentCooldown: number;
    appliesStatus: string | null;
}

const { height } = Dimensions.get('window');

// 輔助函數：從配置創建戰鬥單位
const createUnitFromConfig = (
    cardConfig: CardConfig,
    level: number,
    team: 'player' | 'enemy',
    instanceId: string,
): Unit => {
    // 在這裡可以加入等級對屬性的影響計算
    const atk = cardConfig.baseAttack + cardConfig.attackGrowth * level;
    const maxHp = cardConfig.baseHealth + cardConfig.healthGrowth * level;
    const spd = cardConfig.baseSpeed + cardConfig.speedGrowth * level;

    return {
        id: instanceId,
        team,
        name: cardConfig.name,
        level,
        // TODO: 根據 race 或其他屬性決定圖標
        icon: team === 'player' ? '🤺' : '💀',
        race: cardConfig.race.join(', '),
        rarity: cardConfig.rarity,
        stats: {
            hp: maxHp,
            maxHp,
            mp: 100, // 暫定
            maxMp: 100, // 暫定
            atk,
            spd,
        },
        tags: cardConfig.tags,
        status: [],
        skills: cardConfig.skillIds,
        alive: true,
    };
};

const BattleScreen: React.FC = () => {
    const { configManager, isLoaded, isLoading } = useConfig();
    const { width: screenWidth, height: screenHeight } = useWindowDimensions();
    const [playerTeam, setPlayerTeam] = useState<Unit[]>([]);
    const [enemyTeam, setEnemyTeam] = useState<Unit[]>([]);

    // 扣除導航欄高度（約6%）
    const availableHeight = screenHeight * 0.94;

    useEffect(() => {
        if (isLoaded) {
            console.log('BattleScreen: 配置已加載，正在準備隊伍...');
            prepareTeams();
        }
    }, [isLoaded]);

    const prepareTeams = () => {
        // 獲取第一個關卡配置
        const stage = configManager.getStageConfig('STAGE_001');
        if (!stage) {
            console.error('找不到關卡配置: STAGE_001');
            return;
        }

        // 準備玩家隊伍
        const playerTeamConfig = configManager.getPlayerTeamConfig('PLAYER_TEAM_001');
        if (playerTeamConfig) {
            console.log('玩家隊伍配置:', playerTeamConfig);
            console.log('cardIds 類型:', typeof playerTeamConfig.cardIds, playerTeamConfig.cardIds);
            console.log('cardLevels 類型:', typeof playerTeamConfig.cardLevels, playerTeamConfig.cardLevels);

            // 確保 cardIds 是數組
            const cardIds = Array.isArray(playerTeamConfig.cardIds)
                ? playerTeamConfig.cardIds
                : [playerTeamConfig.cardIds];

            const newPlayerTeam = cardIds.map((cardId, index) => {
                console.log(`🔍 查找卡牌配置: ${cardId}`);
                const cardConfig = configManager.getCardConfig(cardId);
                console.log(`🔍 找到卡牌配置:`, cardConfig);

                // 確保 cardLevels 是數組並獲取對應等級
                const cardLevels = Array.isArray(playerTeamConfig.cardLevels)
                    ? playerTeamConfig.cardLevels
                    : [playerTeamConfig.cardLevels];
                const cardLevel = Number(cardLevels[index] || 1);
                if (cardConfig) {
                    const unit = createUnitFromConfig(cardConfig, cardLevel, 'player', `${cardId}_p${index}`);
                    console.log(`✅ 創建玩家單位:`, unit);
                    return unit;
                } else {
                    console.error(`❌ 找不到卡牌配置: ${cardId}`);
                }
                return null;
            }).filter(unit => unit !== null) as Unit[];
            setPlayerTeam(newPlayerTeam);
            console.log('玩家隊伍已設定:', newPlayerTeam);
        }

        // 準備敵人隊伍 (使用Boss隊伍)
        const enemyDeckId = stage.parts.boss.bossDeckId;
        const enemyDeck = configManager.getEnemyDeckConfig(enemyDeckId);
        if (enemyDeck) {
            console.log('敵人牌組配置:', enemyDeck);
            console.log('敵人 cardIds 類型:', typeof enemyDeck.cardIds, enemyDeck.cardIds);
            console.log('敵人 cardLevels 類型:', typeof enemyDeck.cardLevels, enemyDeck.cardLevels);

            // 確保 cardIds 是數組
            const cardIds = Array.isArray(enemyDeck.cardIds)
                ? enemyDeck.cardIds
                : [enemyDeck.cardIds];

            const newEnemyTeam = cardIds.map((cardId, index) => {
                console.log(`🔍 查找敵人卡牌配置: ${cardId}`);
                const cardConfig = configManager.getCardConfig(cardId);
                console.log(`🔍 找到敵人卡牌配置:`, cardConfig);

                // 確保 cardLevels 是數組並獲取對應等級
                const cardLevels = Array.isArray(enemyDeck.cardLevels)
                    ? enemyDeck.cardLevels
                    : [enemyDeck.cardLevels];
                const cardLevel = Number(cardLevels[index] || 1);
                if (cardConfig) {
                    const unit = createUnitFromConfig(cardConfig, cardLevel, 'enemy', `${cardId}_e${index}`);
                    console.log(`✅ 創建敵人單位:`, unit);
                    return unit;
                } else {
                    console.error(`❌ 找不到敵人卡牌配置: ${cardId}`);
                }
                return null;
            }).filter(unit => unit !== null) as Unit[];
            setEnemyTeam(newEnemyTeam);
            console.log('敵人隊伍已設定:', newEnemyTeam);
        }
    };

    // 版面尺寸（使用 Vintage Paper 主題的佈局比例）
    const theme = VintagePaperTheme;
    const headerHeight = availableHeight * theme.layout.headerRatio;
    const sectionHeight = availableHeight * theme.layout.sectionRatio;

    // 使用主題的卡片尺寸計算函數
    const { cardWidth, cardHeight } = calculateCardSize(screenWidth, sectionHeight);
    const percent = (part: number, whole: number) =>
        Math.max(0, Math.min(100, Math.round((part / whole) * 100)));



    // 使用新的 VintagePaperCard 組件（移除舊的 BattleCard）



    // 渲染玩家單位
    const renderPlayerUnits = () => {
        const placeholders = Array.from({ length: 9 }, (_, index) => {
            const unit = playerTeam[index];
            return unit || {
                id: `p_placeholder_${index}`,
                team: 'player', name: '', level: 0, icon: '', race: '', rarity: 0,
                stats: { hp: 0, maxHp: 0, mp: 0, maxMp: 0, atk: 0, spd: 0 },
                tags: [], status: [], skills: [], alive: false, isEmpty: true,
            };
        });

        // 使用已計算的卡片尺寸

        // 計算總血量
        const totalHp = playerTeam.reduce((sum, unit) => sum + unit.stats.hp, 0);

        return (
            <View style={[styles.playerSection, { height: sectionHeight }]}>
                <SectionHeader
                    team="player"
                    teamName="YOUR TEAM"
                    totalHp={totalHp}
                />
                <View style={styles.cardsGrid}>
                    {placeholders.map((unit) => (
                        <View key={unit.id} style={styles.gridCell}>
                            <VintagePaperCard unit={unit} cardWidth={cardWidth} cardHeight={cardHeight} />
                        </View>
                    ))}
                </View>
            </View>
        );
    };

    // 渲染敵人單位
    const renderEnemyUnits = () => {
        const placeholders = Array.from({ length: 9 }, (_, index) => {
            const unit = enemyTeam[index];
            return unit || {
                id: `e_placeholder_${index}`,
                team: 'enemy', name: '', level: 0, icon: '', race: '', rarity: 0,
                stats: { hp: 0, maxHp: 0, mp: 0, maxMp: 0, atk: 0, spd: 0 },
                tags: [], status: [], skills: [], alive: false, isEmpty: true,
            };
        });

        // 使用已計算的卡片尺寸

        // 計算總血量
        const totalHp = enemyTeam.reduce((sum, unit) => sum + unit.stats.hp, 0);

        return (
            <View style={[styles.enemySection, { height: sectionHeight }]}>
                <SectionHeader
                    team="enemy"
                    teamName="ENEMY"
                    totalHp={totalHp}
                />
                <View style={styles.cardsGrid}>
                    {placeholders.map((unit) => (
                        <View key={unit.id} style={styles.gridCell}>
                            <VintagePaperCard unit={unit} cardWidth={cardWidth} cardHeight={cardHeight} />
                        </View>
                    ))}
                </View>
            </View>
        );
    };

    if (isLoading) {
        return (
            <View style={[styles.container, styles.centerContent]}>
                <ActivityIndicator size="large" color="#8b4513" />
                <Text style={styles.loadingText}>正在載入戰鬥數據...</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            {/* 標題區（8%） */}
            <View style={[styles.headerArea, { height: headerHeight }]}>
                <Text style={styles.stageTitle}>✦ 第 1-1 關：森林小徑</Text>
                <TouchableOpacity style={styles.topControlButton}>
                    <Text style={styles.controlButtonText}>⚙️</Text>
                </TouchableOpacity>
            </View>

            {/* 敵方區（46%） */}
            {renderEnemyUnits()}

            {/* 行動分隔線 */}
            <ActionDivider />

            {/* 我方區（46%） */}
            {renderPlayerUnits()}
        </View>
    );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: VintagePaperTheme.colors.bg0,
    width: '100%',
    height: '100%',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: VintagePaperTheme.colors.ink,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'rgba(139, 69, 19, 0.2)',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(139, 69, 19, 0.3)',
  },
  headerArea: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: VintagePaperTheme.spacing.md,
    paddingBottom: VintagePaperTheme.spacing.md,
    backgroundColor: VintagePaperTheme.colors.bg1,
    position: 'relative',
    minHeight: Math.max(50, height * 0.08),
    borderBottomWidth: 2,
    borderBottomColor: VintagePaperTheme.colors.ink,
    borderStyle: 'dashed',
  },
  stageTitle: {
    fontSize: VintagePaperTheme.typography.stageTitle(height),
    fontWeight: 'bold',
    color: VintagePaperTheme.colors.ink,
    textAlign: 'center',
    transform: [{ rotate: '-1.5deg' }],
  },
  stageTitleInline: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#8b4513',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  headerControls: {
    flexDirection: 'row',
    gap: 10,
  },
  controlButton: {
    width: 36,
    height: 36,
    backgroundColor: '#f4a261',
    borderWidth: 2,
    borderColor: '#8b4513',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    fontSize: Math.max(18, height * 0.022),
    color: VintagePaperTheme.colors.ink,
    fontWeight: 'bold',
  },
  enemySection: {
    backgroundColor: VintagePaperTheme.colors.enemyBg,
    paddingHorizontal: VintagePaperTheme.spacing.lg,
    paddingTop: VintagePaperTheme.spacing.md,
    paddingBottom: VintagePaperTheme.spacing.md,
    position: 'relative',
    justifyContent: 'flex-start',
  },
  playerSection: {
    backgroundColor: VintagePaperTheme.colors.playerBg,
    paddingHorizontal: VintagePaperTheme.spacing.lg,
    paddingTop: VintagePaperTheme.spacing.md,
    paddingBottom: VintagePaperTheme.spacing.md,
    justifyContent: 'flex-start',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  enemyHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#dc2626',
    transform: [{ rotate: '-1deg' }],
  },
  playerHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#228b22',
    transform: [{ rotate: '1deg' }],
  },
  cardsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    flexWrap: 'wrap',
    paddingHorizontal: 6,
    paddingVertical: 6,
    alignContent: 'flex-start',
  },
  gridCell: {
    width: '32%',
    marginBottom: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  // 移除舊的卡片樣式，現在使用 VintagePaperCard 組件
  actionSection: {
    backgroundColor: 'transparent',
    paddingVertical: 6,
    paddingHorizontal: 10,
    alignItems: 'center',
    position: 'relative',
  },
  nextActionText: {
    fontSize: 14,
    marginBottom: 8,
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#8b4513',
    transform: [{ rotate: '-1deg' }],
  },
  turnQueue: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    position: 'relative',
    zIndex: 2,
  },
  turnIndicator: {
    width: 32,
    height: 32,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#8b4513',
  },
  playerIndicator: {
    backgroundColor: '#228b22',
    transform: [{ rotate: '-3deg' }],
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  enemyIndicator: {
    backgroundColor: '#dc2626',
    transform: [{ rotate: '2deg' }],
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  activeIndicator: {
    borderWidth: 3,
    borderColor: '#f4a261',
  },
  turnIndicatorText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
  },
  actionBar: {
    padding: 8,
    backgroundColor: 'rgba(139, 69, 19, 0.15)',
    borderTopWidth: 2,
    borderTopColor: 'rgba(139, 69, 19, 0.4)',
  },
  actionMpText: {
    fontWeight: 'bold',
    marginBottom: 6,
    color: '#3c2415',
  },
  skillBar: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  skillButton: {
    minHeight: 44,
    padding: 8,
    minWidth: 96,
    backgroundColor: '#f9f7f4',
    borderWidth: 2,
    borderColor: '#8b4513',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  skillName: {
    fontWeight: 'bold',
    color: '#3c2415',
  },
  skillCost: {
    fontSize: 11,
    opacity: 0.8,
    color: '#3c2415',
  },
  skillCooldown: {
    fontSize: 11,
    opacity: 0.8,
    color: '#3c2415',
  },
  logContainer: {
    margin: 8,
  },
  logToggle: {
    padding: 8,
    backgroundColor: 'rgba(139, 69, 19, 0.1)',
    borderRadius: 8,
    alignItems: 'center',
  },
  logToggleText: {
    fontWeight: 'bold',
    color: '#8b4513',
  },
  logList: {
    marginTop: 8,
    maxHeight: 150,
  },
  logItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderWidth: 1,
    borderColor: '#8b4513',
    borderStyle: 'dashed',
    borderRadius: 6,
    padding: 6,
    marginBottom: 6,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.25,
    shadowRadius: 0,
    elevation: 1,
  },
  logItemText: {
    color: '#3c2415',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(139, 69, 19, 0.3)',
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 3,
    borderTopColor: 'rgba(139, 69, 19, 0.5)',
  },
  bottomButton: {
    flex: 1,
    padding: 10,
    backgroundColor: '#f4a261',
    borderWidth: 2,
    borderColor: '#8b4513',
    borderRadius: 8,
    marginHorizontal: 5,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 0,
    elevation: 2,
  },
  pauseButton: {
    backgroundColor: 'rgba(220, 38, 38, 0.85)',
  },
  bottomButtonText: {
    color: '#8b4513',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // 移除舊的空卡片樣式，現在使用 VintagePaperCard 組件
  topControlButton: {
    position: 'absolute',
    top: VintagePaperTheme.spacing.md,
    right: VintagePaperTheme.spacing.lg,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
    backgroundColor: VintagePaperTheme.colors.accent,
    borderRadius: VintagePaperTheme.radius.medium,
    borderWidth: 2,
    borderColor: VintagePaperTheme.colors.ink,
    transform: [{ rotate: '-2deg' }],
    ...VintagePaperTheme.shadows.button,
  },
  stageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    backgroundColor: 'transparent',
  },
  actionLine: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: 'rgba(139, 69, 19, 0.4)',
    zIndex: 1,
  },
});

// 添加調試日誌
console.log('BattleScreen.tsx: 導出 BattleScreen 組件');
export default BattleScreen;
