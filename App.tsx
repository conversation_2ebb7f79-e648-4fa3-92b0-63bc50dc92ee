import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, Dimensions } from 'react-native';
import React, { useState, useEffect } from 'react';
import { VintagePaperTheme } from './styles/VintagePaperTheme';

// 添加調試日誌
console.log('App.tsx: 開始導入 BattleScreen');

let BattleScreen: React.ComponentType | null = null;
try {
  BattleScreen = require('./components/BattleScreen').default;
  console.log('App.tsx: BattleScreen 導入成功', BattleScreen);
} catch (error: any) {
  console.error('App.tsx: BattleScreen 導入失敗', error);
  BattleScreen = () => (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ color: 'red', fontSize: 20 }}>
        錯誤: 無法載入 BattleScreen 組件
      </Text>
      <Text style={{ color: 'red', fontSize: 14 }}>
        {error.message}
      </Text>
    </View>
  );
}

import { ConfigProvider } from './contexts/ConfigContext';

const { height } = Dimensions.get('window');

export default function App() {
  console.log('App.tsx: 渲染 App 組件');
  const [activeTab, setActiveTab] = useState('battle');

  const renderContent = () => {
    switch (activeTab) {
      case 'battle':
        return BattleScreen && <BattleScreen />;
      case 'luckyDraw':
        return (
          <View style={styles.placeholderContent}>
            <Text style={styles.placeholderText}>🎰 Lucky Draw</Text>
            <Text style={styles.placeholderSubtext}>抽卡系統即將推出</Text>
          </View>
        );
      case 'teams':
        return (
          <View style={styles.placeholderContent}>
            <Text style={styles.placeholderText}>⚔️ Teams Setting</Text>
            <Text style={styles.placeholderSubtext}>隊伍設定即將推出</Text>
          </View>
        );
      default:
        return BattleScreen && <BattleScreen />;
    }
  };

  return (
    <ConfigProvider>
      <View style={styles.appContainer}>
        {/* 主要內容區域 */}
        <View style={styles.contentArea}>
          {renderContent()}
        </View>

        {/* 底部導航欄 */}
        <View style={styles.navbar}>
          <TouchableOpacity
            style={[styles.navTab, activeTab === 'battle' && styles.activeNavTab]}
            onPress={() => setActiveTab('battle')}
          >
            <Text style={[styles.navTabText, activeTab === 'battle' && styles.activeNavTabText]}>
              ⚔️ Battle
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navTab, activeTab === 'luckyDraw' && styles.activeNavTab]}
            onPress={() => setActiveTab('luckyDraw')}
          >
            <Text style={[styles.navTabText, activeTab === 'luckyDraw' && styles.activeNavTabText]}>
              🎰 Lucky Draw
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navTab, activeTab === 'teams' && styles.activeNavTab]}
            onPress={() => setActiveTab('teams')}
          >
            <Text style={[styles.navTabText, activeTab === 'teams' && styles.activeNavTabText]}>
              👥 Teams
            </Text>
          </TouchableOpacity>
        </View>

        <StatusBar style="auto" />
      </View>
    </ConfigProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  appContainer: {
    flex: 1,
    backgroundColor: VintagePaperTheme.colors.bg0,
  },
  contentArea: {
    flex: 1,
  },
  navbar: {
    height: Math.max(40, height * 0.06),
    flexDirection: 'row',
    backgroundColor: VintagePaperTheme.colors.bg1,
    borderTopWidth: 2,
    borderTopColor: VintagePaperTheme.colors.ink,
    borderStyle: 'dashed',
    paddingVertical: VintagePaperTheme.spacing.md,
    paddingHorizontal: 0,
  },
  navTab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: VintagePaperTheme.spacing.sm,
    paddingHorizontal: VintagePaperTheme.spacing.md,
    borderRadius: VintagePaperTheme.radius.medium,
    marginHorizontal: VintagePaperTheme.spacing.xs,
    borderWidth: 2,
    borderColor: 'transparent',
    transform: [{ rotate: '-1deg' }],
    minHeight: 44,
  },
  activeNavTab: {
    backgroundColor: VintagePaperTheme.colors.accent,
    borderColor: VintagePaperTheme.colors.ink,
    transform: [{ rotate: '1deg' }],
    ...VintagePaperTheme.shadows.button,
  },
  navTabText: {
    fontSize: Math.max(12, height * 0.015),
    fontWeight: 'bold',
    color: VintagePaperTheme.colors.ink,
    textAlign: 'center',
  },
  activeNavTabText: {
    color: '#fff',
  },
  placeholderContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: VintagePaperTheme.colors.bg0,
  },
  placeholderText: {
    fontSize: Math.max(24, height * 0.03),
    fontWeight: 'bold',
    color: VintagePaperTheme.colors.ink,
    marginBottom: 12,
    transform: [{ rotate: '-1deg' }],
  },
  placeholderSubtext: {
    fontSize: Math.max(16, height * 0.02),
    color: VintagePaperTheme.colors.ink,
    opacity: 0.7,
    transform: [{ rotate: '0.5deg' }],
  },
});
