import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, Dimensions } from 'react-native';
import React, { useState, useEffect } from 'react';

// 添加調試日誌
console.log('App.tsx: 開始導入 BattleScreen');

let BattleScreen: React.ComponentType | null = null;
try {
  BattleScreen = require('./components/BattleScreen').default;
  console.log('App.tsx: BattleScreen 導入成功', BattleScreen);
} catch (error: any) {
  console.error('App.tsx: BattleScreen 導入失敗', error);
  BattleScreen = () => (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ color: 'red', fontSize: 20 }}>
        錯誤: 無法載入 BattleScreen 組件
      </Text>
      <Text style={{ color: 'red', fontSize: 14 }}>
        {error.message}
      </Text>
    </View>
  );
}

import { ConfigProvider } from './contexts/ConfigContext';

const { height } = Dimensions.get('window');

export default function App() {
  console.log('App.tsx: 渲染 App 組件');
  const [activeTab, setActiveTab] = useState('battle');

  const renderContent = () => {
    switch (activeTab) {
      case 'battle':
        return BattleScreen && <BattleScreen />;
      case 'luckyDraw':
        return (
          <View style={styles.placeholderContent}>
            <Text style={styles.placeholderText}>🎰 Lucky Draw</Text>
            <Text style={styles.placeholderSubtext}>抽卡系統即將推出</Text>
          </View>
        );
      case 'teams':
        return (
          <View style={styles.placeholderContent}>
            <Text style={styles.placeholderText}>⚔️ Teams Setting</Text>
            <Text style={styles.placeholderSubtext}>隊伍設定即將推出</Text>
          </View>
        );
      default:
        return BattleScreen && <BattleScreen />;
    }
  };

  return (
    <ConfigProvider>
      <View style={styles.appContainer}>
        {/* 主要內容區域 */}
        <View style={styles.contentArea}>
          {renderContent()}
        </View>

        {/* 底部導航欄 */}
        <View style={styles.navbar}>
          <TouchableOpacity
            style={[styles.navTab, activeTab === 'battle' && styles.activeNavTab]}
            onPress={() => setActiveTab('battle')}
          >
            <Text style={[styles.navTabText, activeTab === 'battle' && styles.activeNavTabText]}>
              ⚔️ Battle
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navTab, activeTab === 'luckyDraw' && styles.activeNavTab]}
            onPress={() => setActiveTab('luckyDraw')}
          >
            <Text style={[styles.navTabText, activeTab === 'luckyDraw' && styles.activeNavTabText]}>
              🎰 Lucky Draw
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navTab, activeTab === 'teams' && styles.activeNavTab]}
            onPress={() => setActiveTab('teams')}
          >
            <Text style={[styles.navTabText, activeTab === 'teams' && styles.activeNavTabText]}>
              👥 Teams
            </Text>
          </TouchableOpacity>
        </View>

        <StatusBar style="auto" />
      </View>
    </ConfigProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  appContainer: {
    flex: 1,
    backgroundColor: '#f7f3e9',
  },
  contentArea: {
    flex: 1,
  },
  navbar: {
    height: Math.max(40, height * 0.06),
    flexDirection: 'row',
    backgroundColor: '#8b4513',
    borderTopWidth: 2,
    borderTopColor: '#654321',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 8,
    paddingVertical: 4,
  },
  navTab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
    borderRightWidth: 1,
    borderRightColor: 'rgba(255, 255, 255, 0.2)',
  },
  activeNavTab: {
    backgroundColor: 'rgba(244, 162, 97, 0.3)',
    borderTopWidth: 3,
    borderTopColor: '#f4a261',
  },
  navTabText: {
    fontSize: Math.max(14, height * 0.018),
    fontWeight: 'bold',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  activeNavTabText: {
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  placeholderContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f7f3e9',
  },
  placeholderText: {
    fontSize: Math.max(24, height * 0.03),
    fontWeight: 'bold',
    color: '#8b4513',
    marginBottom: 12,
  },
  placeholderSubtext: {
    fontSize: Math.max(16, height * 0.02),
    color: '#8b4513',
    opacity: 0.7,
  },
});
