# Vintage Paper 戰鬥界面設計指南

## 設計概念

Vintage Paper 風格以溫暖的米色調和手繪質感為特色，營造出親切、溫馨的遊戲體驗。基於 `battle-ui-vintage-paper.html` 的最新設計規範。

### 色彩規範 (Vintage Paper Theme)

#### 主要色彩
- **背景紙**: `#f7f1e3` (--bg-0)
- **次層紙**: `#efe7d4` (--bg-1)
- **紙張邊緣**: `#e4d6b9` (--paper-edge)
- **手寫墨色**: `#3b2b22` (--ink)

#### 強調色彩
- **焦糖/赭石**: `#c46a2b` (--accent) - 攻擊力、按鈕
- **互補藍灰**: `#5b7a8d` (--accent-2) - 提示/技能
- **我方/治癒**: `#2f6f4f` (--good) - 速度、我方元素
- **敵方/傷害**: `#a7302b` (--bad) - 敵方元素

#### 透明度變體
- **敵方背景**: `rgba(167, 48, 43, 0.06)`
- **我方背景**: `rgba(47, 111, 79, 0.06)`
- **行動分隔線**: `rgba(196, 106, 43, 0.08)`

## 戰鬥界面佈局結構

### 整體佈局比例
- **標題區**: 8% (headerRatio: 0.08)
- **敵方區**: 46% (sectionRatio: 0.46)
- **行動分隔線**: 自適應高度
- **我方區**: 46% (sectionRatio: 0.46)

### 卡片基礎規格 (最新設計)
```css
寬度: 78px (標準) / 70px (小螢幕)
高度: 105px (標準) / 95px (小螢幕)
邊框: 2px solid var(--ink)
圓角: 6px
陰影: 0 6px 16px rgba(59, 43, 34, 0.15)
```

### 卡片組件結構 (移除等級/費用)

#### 1. 圖標區域 (Card Icon) - 頂部
- **位置**: 卡片頂部，margin-top: 4px
- **大小**: 18px (標準) / 16px (小螢幕)
- **特效**:
  - sepia(0.25) contrast(1.05)
  - text-align: center

#### 2. 卡片名稱 (Card Name) - 圖標下方
- **位置**: 圖標下方，margin: 2px 0 3px
- **字體**: 9px (標準) / 8px (小螢幕), bold
- **特效**:
  - text-decoration: underline
  - text-decoration-color: rgba(59, 43, 34, 0.25)

#### 3. 屬性數值 (Card Stats)
- **位置**: 名稱下方
- **佈局**: 左右分佈 (攻擊力 vs 速度)
- **字體**: 8px (標準) / 7px (小螢幕), bold
- **顏色**:
  - 攻擊力: var(--accent) `#c46a2b`
  - 速度: var(--good) `#2f6f4f`

#### 4. 血條 (HP Bar)
- **位置**: margin-top: 3px
- **尺寸**: width: 90%, height: 4px
- **樣式**:
  - 背景: rgba(59, 43, 34, 0.18)
  - 邊框: 1px solid var(--ink)
  - 圓角: 2px
- **血條顏色**:
  - 敵方: linear-gradient(90deg, #dc4b3f, #a7302b)
  - 我方: linear-gradient(90deg, #2e7d4f, #1e5f3a)

### 區域標題 (Section Headers)
- **敵方**: 🔴 ENEMY + ❤️ 總血量
- **我方**: 🔵 YOUR TEAM + ❤️ 總血量
- **字體**: 14px, bold
- **特效**:
  - 敵方: color: var(--bad), transform: rotate(-1deg)
  - 我方: color: var(--good), transform: rotate(1deg)

### 行動分隔線 (Action Divider)
- **位置**: 敵我區域之間
- **背景**: rgba(196, 106, 43, 0.08)
- **線條**: height: 3px, 漸變效果
- **圖標**: ⚡ 居中，圓形背景
- **尺寸**: width: 24px, height: 24px

## 視覺特效

### 紙張纖維質感
```css
body:before {
  background:
    radial-gradient(circle at 20% 30%, rgba(59,43,34,.08) 1px, transparent 1px) 0 0/22px 22px,
    radial-gradient(circle at 70% 60%, rgba(59,43,34,.06) 1px, transparent 1px) 0 0/18px 18px;
  mix-blend-mode: multiply;
  opacity: 0.25;
}
```

### 卡片虛線內框
```css
.card:after {
  border: 1px dashed var(--ink);
  opacity: 0.35;
  inset: 3px;
  border-radius: 4px;
}
```

### 互動效果

#### Hover 狀態
```css
transform: translateY(-2px) rotate(0.8deg);
transition: 0.2s;
```

### 手繪風格旋轉
- **標題**: transform: rotate(-1.5deg)
- **按鈕**: transform: rotate(-2deg)
- **導航標籤**: transform: rotate(-1deg) / rotate(1deg)

## 響應式適配

### 小螢幕優化 (≤360px)
```css
卡片尺寸: 70px × 95px
圖標大小: 16px
名稱字體: 8px
屬性字體: 7px
網格間距: 4px
```

## React Native 實現指南

### 主題系統 (VintagePaperTheme.ts)
```typescript
export const VintagePaperTheme = {
  colors: {
    bg0: '#f7f1e3',      // 背景紙
    bg1: '#efe7d4',      // 次層紙
    ink: '#3b2b22',      // 手寫墨色
    accent: '#c46a2b',   // 焦糖/赭石
    good: '#2f6f4f',     // 我方/治癒
    bad: '#a7302b',      // 敵方/傷害
    // ... 更多顏色
  },
  layout: {
    headerRatio: 0.08,
    sectionRatio: 0.46,
  },
  // ... 其他主題設定
};
```

### 組件結構 (最新設計)
```tsx
// VintagePaperCard.tsx - 移除等級/費用
<View style={styles.card}>
  {/* 虛線內邊框 */}
  <View style={styles.cardInnerBorder} />

  {/* 圖標在頂部 */}
  <Text style={styles.cardIcon}>{icon}</Text>

  {/* 名稱在圖標下方 */}
  <Text style={styles.cardName}>{name}</Text>

  {/* 屬性 */}
  <View style={styles.cardStats}>
    <Text style={styles.cardAtk}>⚔️{attack}</Text>
    <Text style={styles.cardSpd}>⚡{speed}</Text>
  </View>

  {/* 血條 */}
  <View style={styles.hpBar}>
    <View style={styles.hpFill} />
  </View>
</View>

// ActionDivider.tsx - 行動分隔線
<View style={styles.actionDivider}>
  <View style={styles.actionLine}>
    <View style={styles.actionIcon}>
      <Text>⚡</Text>
    </View>
  </View>
</View>

// SectionHeader.tsx - 區域標題
<View style={styles.sectionHeader}>
  <Text style={styles.teamText}>🔴 ENEMY</Text>
  <Text style={styles.hpText}>❤️ {totalHp}</Text>
</View>
```

### 關鍵樣式實現
- 使用 `VintagePaperTheme` 統一管理顏色和尺寸
- 利用 `transform: [{ rotate: 'Xdeg' }]` 實現手繪旋轉效果
- 通過 `shadowColor` 和 `elevation` 模擬紙張陰影
- 使用 `borderStyle: 'dashed'` 創建虛線效果
- 響應式字體大小基於螢幕高度計算

### 檔案結構
```
styles/
  VintagePaperTheme.ts     # 主題系統
components/
  VintagePaperCard.tsx     # 卡片組件
  ActionDivider.tsx        # 行動分隔線
  SectionHeader.tsx        # 區域標題
  BattleScreen.tsx         # 戰鬥界面
App.tsx                    # 主應用
```

此 Vintage Paper 風格營造溫馨、手工的遊戲氛圍，適合休閒卡牌遊戲，強調簡潔性和可讀性。