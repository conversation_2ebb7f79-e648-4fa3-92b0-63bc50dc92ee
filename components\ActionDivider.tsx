import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { VintagePaperTheme } from '../styles/VintagePaperTheme';

interface ActionDividerProps {
  icon?: string;
}

const ActionDivider: React.FC<ActionDividerProps> = ({ icon = '⚡' }) => {
  return (
    <View style={styles.actionDivider}>
      <View style={styles.actionLine}>
        <View style={styles.actionIcon}>
          <Text style={styles.actionIconText}>{icon}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  actionDivider: {
    paddingVertical: VintagePaperTheme.spacing.md,
    paddingHorizontal: VintagePaperTheme.spacing.xxl,
    backgroundColor: VintagePaperTheme.colors.actionDividerBg,
    position: 'relative',
  },
  
  actionLine: {
    height: 3,
    backgroundColor: VintagePaperTheme.colors.accent,
    borderRadius: 2,
    position: 'relative',
    // 模擬漸變效果（React Native 需要額外庫來實現真正的漸變）
    opacity: 0.8,
  },
  
  actionIcon: {
    position: 'absolute',
    top: -12,
    left: '50%',
    marginLeft: -12, // 一半的寬度
    backgroundColor: VintagePaperTheme.colors.bg0,
    borderWidth: 2,
    borderColor: VintagePaperTheme.colors.accent,
    borderRadius: VintagePaperTheme.radius.round,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    ...VintagePaperTheme.shadows.card,
  },
  
  actionIconText: {
    fontSize: 14,
    color: VintagePaperTheme.colors.accent,
  },
});

export default ActionDivider;
