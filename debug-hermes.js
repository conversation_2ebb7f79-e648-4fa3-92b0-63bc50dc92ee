// 調試腳本：檢查 Hermes 和 DevTools 狀態
const fs = require('fs');
const path = require('path');

console.log('=== React Native DevTools 調試報告 ===\n');

// 檢查 package.json
console.log('1. 檢查 package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log('   React Native 版本:', packageJson.dependencies['react-native']);
  console.log('   Expo 版本:', packageJson.dependencies['expo']);
  console.log('   React 版本:', packageJson.dependencies['react']);
} catch (error) {
  console.log('   ❌ 無法讀取 package.json:', error.message);
}

// 檢查 app.json
console.log('\n2. 檢查 app.json...');
try {
  const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
  console.log('   JS 引擎設置:', appJson.expo.jsEngine || '未設置');
  console.log('   新架構啟用:', appJson.expo.newArchEnabled);
  console.log('   應用名稱:', appJson.expo.name);
} catch (error) {
  console.log('   ❌ 無法讀取 app.json:', error.message);
}

// 檢查 metro.config.js
console.log('\n3. 檢查 metro.config.js...');
try {
  const metroConfig = fs.readFileSync('metro.config.js', 'utf8');
  const hasHermesParser = metroConfig.includes('hermesParser: true');
  const hasServerConfig = metroConfig.includes('config.server');
  console.log('   Hermes Parser 啟用:', hasHermesParser);
  console.log('   服務器配置存在:', hasServerConfig);
} catch (error) {
  console.log('   ❌ 無法讀取 metro.config.js:', error.message);
}

// 檢查環境變量
console.log('\n4. 檢查環境變量...');
console.log('   NODE_ENV:', process.env.NODE_ENV || '未設置');
console.log('   EXPO_DEBUG:', process.env.EXPO_DEBUG || '未設置');

console.log('\n=== 調試完成 ===');
console.log('\n建議的修復步驟：');
console.log('1. 確保在 app.json 中設置 "jsEngine": "hermes"');
console.log('2. 在 metro.config.js 中啟用 hermesParser');
console.log('3. 清理快取：npx expo start --clear');
console.log('4. 嘗試使用原生平台而非 Web 平台進行開發');