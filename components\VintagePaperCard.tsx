import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { VintagePaperTheme, percent } from '../styles/VintagePaperTheme';

interface Unit {
  id: string;
  team: 'player' | 'enemy';
  name: string;
  level: number;
  icon: string;
  race: string;
  rarity: number;
  stats: {
    hp: number;
    maxHp: number;
    mp: number;
    maxMp: number;
    atk: number;
    spd: number;
  };
  tags: string[];
  status: Status[];
  skills: string[];
  alive: boolean;
  isEmpty?: boolean;
}

interface Status {
  statusId: string;
  stacks: number;
  duration: number;
}

interface VintagePaperCardProps {
  unit: Unit;
  cardWidth: number;
  cardHeight: number;
}

const VintagePaperCard: React.FC<VintagePaperCardProps> = ({ unit, cardWidth, cardHeight }) => {
  const theme = VintagePaperTheme;
  
  // 動態字體大小
  const iconSize = theme.typography.cardIcon(cardHeight);
  const nameSize = theme.typography.cardName(cardHeight);
  const statsSize = theme.typography.cardStats(cardHeight);
  
  // 如果是空佔位符，顯示空卡片
  if (unit.isEmpty) {
    return (
      <View style={[
        styles.card,
        styles.emptyCard,
        { width: cardWidth, height: cardHeight }
      ]}>
        <View style={styles.emptyCardContent}>
          <Text style={[styles.emptyCardText, { fontSize: nameSize }]}>空</Text>
        </View>
      </View>
    );
  }

  return (
    <View
      style={[
        styles.card,
        { width: cardWidth, height: cardHeight },
        unit.team === 'enemy' ? styles.enemyCard : styles.playerCard,
        !unit.alive && styles.koCard,
      ]}
    >
      {/* 虛線邊框效果 */}
      <View style={[styles.cardInnerBorder, { width: cardWidth - 6, height: cardHeight - 6 }]} />
      
      {/* 圖標 - 在頂部 */}
      <Text style={[styles.cardIcon, { fontSize: iconSize, marginTop: theme.spacing.sm }]}>
        {unit.icon}
      </Text>
      
      {/* 名稱 - 在圖標下方 */}
      <Text
        style={[styles.cardName, { fontSize: nameSize, maxWidth: cardWidth - 8 }]}
        numberOfLines={1}
        ellipsizeMode="tail"
      >
        {unit.name}
      </Text>
      
      {/* 屬性 */}
      <View style={styles.cardStats}>
        <Text style={[styles.cardAtk, { fontSize: statsSize }]}>⚔️{unit.stats.atk}</Text>
        <Text style={[styles.cardSpd, { fontSize: statsSize }]}>⚡{unit.stats.spd}</Text>
      </View>
      
      {/* 血條 */}
      <View style={[styles.hpBar, { height: Math.max(4, cardHeight * 0.04) }]}>
        <View
          style={[
            styles.hpFill,
            { width: `${percent(unit.stats.hp, unit.stats.maxHp)}%` },
            unit.team === 'player' ? styles.playerHpFill : styles.enemyHpFill,
          ]}
        />
      </View>
      
      {/* 狀態圖標 */}
      <View style={styles.statusRow}>
        {unit.status.map((status, index) => (
          <Text key={index} style={[styles.statusIcon, { fontSize: statsSize }]}>
            {status.statusId === 'haste' ? '⚡' : '🛡️'}
          </Text>
        ))}
      </View>
      
      {/* KO 標記 */}
      {!unit.alive && (
        <Text style={[styles.koText, { fontSize: nameSize }]}>KO</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: VintagePaperTheme.colors.bg0,
    borderWidth: 2,
    borderColor: VintagePaperTheme.colors.ink,
    borderRadius: VintagePaperTheme.radius.small,
    padding: VintagePaperTheme.spacing.sm,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'flex-start',
    ...VintagePaperTheme.shadows.card,
  },
  
  enemyCard: {
    borderColor: VintagePaperTheme.colors.enemyBorder,
  },
  
  playerCard: {
    borderColor: VintagePaperTheme.colors.playerBorder,
  },
  
  koCard: {
    opacity: 0.45,
  },
  
  // 虛線內邊框效果（模擬 CSS :after）
  cardInnerBorder: {
    position: 'absolute',
    top: 3,
    left: 3,
    borderWidth: 1,
    borderColor: VintagePaperTheme.colors.ink,
    borderStyle: 'dashed',
    borderRadius: VintagePaperTheme.radius.small - 2,
    opacity: 0.35,
    pointerEvents: 'none',
  },
  
  cardIcon: {
    textAlign: 'center',
    zIndex: 2,
  },
  
  cardName: {
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: VintagePaperTheme.spacing.xs,
    color: VintagePaperTheme.colors.ink,
    zIndex: 2,
    textDecorationLine: 'underline',
    textDecorationColor: 'rgba(59, 43, 34, 0.25)',
  },
  
  cardStats: {
    flexDirection: 'row',
    gap: VintagePaperTheme.spacing.sm,
    fontWeight: 'bold',
    color: VintagePaperTheme.colors.ink,
    zIndex: 2,
  },
  
  cardAtk: {
    color: VintagePaperTheme.colors.accent,
    fontWeight: 'bold',
  },
  
  cardSpd: {
    color: VintagePaperTheme.colors.good,
    fontWeight: 'bold',
  },
  
  hpBar: {
    width: '90%',
    backgroundColor: 'rgba(59, 43, 34, 0.18)',
    borderRadius: 2,
    overflow: 'hidden',
    marginTop: VintagePaperTheme.spacing.xs,
    borderWidth: 1,
    borderColor: VintagePaperTheme.colors.ink,
    zIndex: 2,
  },
  
  hpFill: {
    height: '100%',
  },
  
  playerHpFill: {
    backgroundColor: VintagePaperTheme.colors.good,
  },
  
  enemyHpFill: {
    backgroundColor: VintagePaperTheme.colors.bad,
  },
  
  statusRow: {
    flexDirection: 'row',
    gap: VintagePaperTheme.spacing.sm,
    marginTop: VintagePaperTheme.spacing.xs,
    zIndex: 2,
  },
  
  statusIcon: {
    lineHeight: 12,
  },
  
  koText: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: '#8b0000',
    color: '#fff',
    borderWidth: 2,
    borderColor: VintagePaperTheme.colors.ink,
    borderRadius: VintagePaperTheme.radius.small,
    paddingVertical: 0,
    paddingHorizontal: 4,
    zIndex: 4,
    transform: [{ rotate: '-10deg' }],
    fontWeight: 'bold',
  },
  
  // 空卡片樣式
  emptyCard: {
    backgroundColor: '#f5f5f5',
    borderColor: '#ccc',
    borderStyle: 'dashed',
    opacity: 0.6,
  },
  
  emptyCardContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  emptyCardText: {
    color: '#999',
    fontWeight: 'bold',
  },
});

export default VintagePaperCard;
