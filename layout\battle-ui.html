<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PetingGame 戰鬥 UI</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: 'Times New Roman', serif;
      background: linear-gradient(135deg, #f7f3e9 0%, #e8dcc0 50%, #d4c5a0 100%);
      min-height: 100vh;
      color: #3c2415;
      overflow-x: hidden;
    }
    .battle-container {
      max-width: 375px;
      margin: 0 auto;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      position: relative;
      padding-bottom: 10px;
    }
    /* Header */
    .battle-header#battle-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: rgba(139, 69, 19, 0.2);
      border-bottom: 2px dashed rgba(139,69,19,0.3);
      position: sticky;
      top: 0;
      z-index: 10;
      backdrop-filter: blur(6px);
    }
    .stage-info#stage-title {
      font-size: 16px;
      font-weight: bold;
      color: #8b4513;
      transform: rotate(-2deg);
    }
    .header-controls { display:flex; gap:10px; }
    .control-btn {
      width: 36px; height: 36px;
      background: #f4a261;
      border: 2px solid #8b4513; border-radius: 8px;
      color: #8b4513; font-size: 16px;
      cursor: pointer; transition: .2s ease;
      box-shadow: 2px 2px 0 rgba(139,69,19,.3);
      transform: rotate(-2deg);
    }
    .control-btn:hover { background:#f59e0b; transform: rotate(0) scale(1.05); }
    .control-btn:focus-visible { outline: 3px dashed #8b4513; outline-offset: 2px; }

    /* Section headers */
    .section-header { display:flex; justify-content:space-between; align-items:center; margin: 8px 8px 12px; font-size: 14px; font-weight: bold; }
    .enemy-section#enemy-section { background: rgba(220,38,38,.08); padding: 12px 8px 14px; border-bottom: 3px dashed rgba(139,69,19,.3); min-height: 30vh; }
    .section-header.enemy-header { color:#dc2626; transform: rotate(-1deg); }
    .player-section#player-section { background: rgba(34,139,34,.08); padding: 12px 8px 14px; border-bottom: 3px dashed rgba(139,69,19,.3); min-height: 40vh; }
    .section-header.player-header { color:#228b22; transform: rotate(1deg); }

    /* Cards grid */
    .cards-grid, .player-cards { display: flex; justify-content: center; gap: 10px; flex-wrap: wrap; padding: 0 8px; }

    /* Battle cards (hand-drawn style) */
    .battle-card {
      width: 100px; height: 140px;
      background: linear-gradient(145deg, #f9f7f4, #f0ede6);
      border: 3px solid #8b4513; border-radius: 8px;
      position: relative; cursor: pointer; transition: .2s ease;
      display:flex; flex-direction: column; align-items:center; justify-content: flex-start;
      padding: 6px 4px; font-size: 12px;
      box-shadow: 0 6px 20px rgba(139,69,19,.3); filter: drop-shadow(2px 2px 4px rgba(0,0,0,.1));
    }
    .battle-card::before {
      content:''; position:absolute; inset:0;
      background:
        radial-gradient(circle at 20% 30%, rgba(139,69,19,.1) 2px, transparent 2px),
        radial-gradient(circle at 70% 60%, rgba(139,69,19,.05) 1px, transparent 1px),
        radial-gradient(circle at 40% 80%, rgba(139,69,19,.08) 1px, transparent 1px);
      background-size: 20px 20px, 15px 15px, 25px 25px;
      z-index:1;
    }
    .battle-card::after {
      content:''; position:absolute; inset:6px;
      border: 2px dashed #8b4513; border-radius:4px; opacity:.3; z-index:1;
    }
    .battle-card .card-level {
      position: absolute; top: -6px; left: -6px;
      background:#f4a261; color:#fff; border:2px solid #8b4513; border-radius:50%;
      width: 22px; height:22px; display:flex; align-items:center; justify-content:center;
      font-weight: bold; font-size: 11px; z-index:3; box-shadow:2px 2px 0 rgba(139,69,19,.3);
      transform: rotate(-5deg);
    }
    .battle-card .card-icon { font-size: 28px; margin-top: 14px; z-index:2; position:relative; filter: sepia(.3) contrast(1.2); animation: handBob 4s ease-in-out infinite; transform: rotate(-2deg); }
    .battle-card .card-name { font-size: 12px; font-weight: bold; text-align: center; margin: 4px 0 2px; color:#8b4513; z-index:2; position:relative; transform: rotate(1deg); text-decoration: underline; text-decoration-color: rgba(139,69,19,.3); }
    .battle-card .card-stats { display:flex; gap:6px; font-size: 11px; font-weight: bold; color:#8b4513; z-index:2; position:relative; }
    .battle-card .card-stats .atk { color:#d2691e; transform: rotate(-1deg); }
    .battle-card .card-stats .spd { color:#228b22; transform: rotate(1deg); }

    .hp-bar { width: 92%; height: 6px; background: rgba(139,69,19,.2); border-radius: 3px; overflow: hidden; margin-top: 6px; border:1px solid #8b4513; z-index:2; position:relative; }
    .hp-fill { height: 100%; background: linear-gradient(90deg, #dc2626, #b91c1c); transition: width .35s ease; position: relative; }
    .hp-fill.player { background: linear-gradient(90deg, #228b22, #006400); }
    .hp-fill::after {
      content:''; position:absolute; inset:0;
      background: repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255,255,255,.3) 2px, rgba(255,255,255,.3) 4px);
    }

    .status-row { display:flex; gap:4px; margin-top: 4px; min-height: 16px; z-index: 2; position:relative; }
    .status-icon { font-size: 13px; line-height: 1; filter: drop-shadow(1px 1px 0 rgba(139,69,19,.2)); }
    .status-icon[data-status-id="haste"] { color:#228b22; }
    .status-icon[data-status-id="guarded"] { color:#8b4513; }

    .battle-card.enemy { border-color: rgba(220,38,38,.85); }
    .battle-card.player { border-color: rgba(34,139,34,.85); }

    .battle-card:hover { transform: translateY(-3px) rotate(2deg); box-shadow: 0 12px 30px rgba(139,69,19,.4); }
    .battle-card:focus-visible { outline: 3px dashed #8b4513; outline-offset: 2px; }

    .active-turn { box-shadow: 0 0 0 3px #f4a261 inset, 0 0 14px rgba(244,162,97,.6); animation: pulse 1.4s infinite; }
    .targetable { box-shadow: 0 0 0 3px rgba(59,130,246,.8) inset, 0 0 12px rgba(59,130,246,.6); }
    .untargetable { opacity: .4; filter: grayscale(.3); pointer-events: none; }
    .disabled { opacity: .5; filter: grayscale(.4); pointer-events: none; }
    .ko { opacity: .45; filter: grayscale(1); }
    .ko::before { content:"KO"; position:absolute; top: 4px; right: 4px; background:#8b0000; color:#fff; border:2px solid #3c2415; border-radius: 6px; padding: 0 4px; font-size: 10px; z-index:4; transform: rotate(-10deg); }

    /* Action section */
    .action-section#action-section {
      background: rgba(139, 69, 19, 0.15);
      padding: 10px 12px;
      border-top: 2px dashed rgba(139, 69, 19, 0.3);
      border-bottom: 2px dashed rgba(139, 69, 19, 0.3);
      backdrop-filter: blur(4px);
    }
    .next-action#next-action { font-size: 14px; margin-bottom: 8px; text-align: center; font-weight:bold; color:#8b4513; transform: rotate(-1deg); }
    .action-queue#turn-queue { display:flex; justify-content:center; align-items:center; gap:8px; }
    .action-indicator { width: 32px; height: 32px; border-radius: 50%; display:flex; align-items:center; justify-content:center; font-size: 14px; font-weight: bold; border: 2px solid #8b4513; }
    .action-indicator.player { background:#228b22; color:#fff; transform: rotate(-3deg); box-shadow: 2px 2px 0 rgba(139,69,19,.3); }
    .action-indicator.enemy { background:#dc2626; color:#fff; transform: rotate(2deg); box-shadow: 2px 2px 0 rgba(139,69,19,.3); }
    .action-indicator.active { outline: 3px dashed #f4a261; outline-offset: 2px; }

    /* Action bar */
    #action-bar { position: sticky; bottom: 58px; padding: 8px 10px; background: rgba(139,69,19,.15); border-top: 2px dashed rgba(139,69,19,.4); display:none; z-index:9; }
    #action-mp { font-weight: bold; margin-bottom: 6px; color:#3c2415; }
    #skill-bar { display:flex; gap:8px; flex-wrap: wrap; justify-content: center; }
    .skill-btn {
      min-height: 44px; padding: 8px 10px; min-width: 96px;
      background:#f9f7f4; border:2px solid #8b4513; border-radius: 10px; color:#3c2415;
      box-shadow: 2px 2px 0 rgba(139,69,19,.3); cursor:pointer; transition:.15s ease; transform: rotate(-1deg);
    }
    .skill-btn:hover { background:#f0ede6; transform: translateY(-1px) rotate(0); }
    .skill-btn .skill-name { display:block; font-weight: bold; }
    .skill-btn .skill-cost, .skill-btn .skill-cd { display:block; font-size: 11px; opacity: .8; }
    .skill-btn.selected { border-color:#3b82f6; box-shadow: 0 0 12px rgba(59,130,246,.5); }
    .skill-btn.disabled { opacity:.5; filter: grayscale(.4); cursor:not-allowed; }
    #btn-cancel-selection { margin-top: 8px; display:none; background:#f4a261; color:#3c2415; border:2px solid #8b4513; border-radius:8px; padding:6px 10px; font-weight:bold; }
    #btn-cancel-selection:focus-visible { outline: 3px dashed #8b4513; outline-offset: 2px; }

    /* Bottom controls */
    .battle-controls { position: sticky; bottom: 0; background: rgba(139,69,19,.3); padding: 10px; display:flex; gap:10px; justify-content: space-around; border-top: 3px dashed rgba(139,69,19,.5); backdrop-filter: blur(8px); }
    .battle-btn { flex:1; padding: 10px 8px; background:#f4a261; border:2px solid #8b4513; border-radius:8px; color:#8b4513; font-size: 12px; font-weight:bold; cursor:pointer; transform: rotate(-1deg); box-shadow: 2px 2px 0 rgba(139,69,19,.3); }
    .battle-btn.speed#btn-speed { background: rgba(34,139,34,.85); color:#fff; transform: rotate(1deg); }
    .battle-btn.pause#btn-pause-bottom { background: rgba(220,38,38,.85); color:#fff; transform: rotate(-2deg); }
    .battle-btn:focus-visible { outline: 3px dashed #8b4513; outline-offset: 2px; }

    /* Logs */
    #log-panel { margin: 8px 10px; }
    #log-toggle { font-weight: bold; cursor: pointer; }
    #log-list[aria-live="polite"] { margin-top: 8px; display:flex; flex-direction: column; gap:6px; list-style: none; }
    .log-item { background: rgba(255,255,255,.5); border:1px dashed #8b4513; border-radius: 6px; padding: 6px 8px; box-shadow: 1px 1px 0 rgba(139,69,19,.25); }

    /* Overlay + floating indicators */
    #target-overlay { position: fixed; inset: 0; background: rgba(60,36,21,.15); display:none; z-index: 8; pointer-events: none; }
    #floating-indicators { position: absolute; inset: 0; pointer-events: none; }
    .float-indicator { position: absolute; left: 50%; transform: translateX(-50%); padding: 2px 6px; border-radius: 6px; border:2px solid #8b4513; background:#fff; font-weight: bold; z-index: 5; opacity: 0; animation: floatUp .6s ease forwards; }
    .float-indicator.dmg { color:#dc2626; background:#ffecec; }
    .float-indicator.heal { color:#228b22; background:#eaffea; }
    .float-indicator.buff { color:#3b82f6; background:#eaf2ff; }
    .float-indicator.debuff { color:#8b0000; background:#ffe5e5; }

    /* Animations */
    @keyframes pulse { 0%,100%{transform:scale(1)} 50%{transform:scale(1.03)} }
    @keyframes hitShake {
      0%{transform: translateX(0) rotate(0);} 25%{transform: translateX(-2px) rotate(-2deg);}
      50%{transform: translateX(2px) rotate(2deg);} 75%{transform: translateX(-1px) rotate(-1deg);} 100%{transform: translateX(0) rotate(0);}
    }
    .damage-animation { animation: hitShake .35s ease; }
    @keyframes handBob { 0%,100%{transform: translateY(0) rotate(-2deg);} 50%{transform: translateY(-2px) rotate(0);} }
    @keyframes floatUp { 0%{opacity:0; transform: translate(-50%, 8px);} 60%{opacity:1;} 100%{opacity:0; transform: translate(-50%, -18px);} }

    /* Accessibility focus */
    :focus-visible { outline: 3px dashed #8b4513; outline-offset: 2px; }

    /* Responsive (<=360px) */
    @media (max-width: 360px) {
      .battle-container { max-width: 100%; }
      .battle-card { width: 85px; height: 120px; }
      .battle-card .card-icon { font-size: 24px; }
      .action-indicator { width: 28px; height: 28px; font-size: 12px; }
      .skill-btn { min-width: 88px; }
    }
  </style>
</head>
<body>
  <div class="battle-container" id="battlefield">
    <!-- Header -->
    <div class="battle-header" id="battle-header">
      <div class="stage-info" id="stage-title" aria-label="關卡標題">⚔️ Stage 1-5</div>
      <div class="header-controls">
        <button class="control-btn" id="btn-pause" aria-label="暫停/繼續" title="暫停">⏸️</button>
        <button class="control-btn" id="btn-sfx" aria-label="切換音效" title="音效">🔊</button>
        <button class="control-btn" id="btn-settings" aria-label="設定" title="設定">⚙️</button>
      </div>
    </div>

    <!-- Enemy Section -->
    <div class="enemy-section" id="enemy-section">
      <div class="section-header enemy-header">
        <span>🔴 ENEMY (<span id="enemy-count">0/0</span>)</span>
        <span>❤️ <span id="enemy-total-hp">0</span></span>
      </div>
      <div class="cards-grid" id="enemy-cards" aria-label="敵方卡片區"></div>
    </div>

    <!-- Action Section -->
    <div class="action-section" id="action-section">
      <div class="next-action" id="next-action">⚡ 下一個: -</div>
      <div class="action-queue" id="turn-queue" role="list" aria-label="回合順序"></div>
    </div>

    <!-- Player Section -->
    <div class="player-section" id="player-section">
      <div class="section-header player-header">
        <span>🔵 YOUR TEAM (<span id="player-count">0/0</span>)</span>
        <span>❤️ <span id="player-total-hp">0</span></span>
      </div>
      <div class="player-cards" id="player-cards" aria-label="我方卡片區"></div>
    </div>

    <!-- Action Bar -->
    <div id="action-bar" aria-label="技能操作列">
      <div id="action-mp">MP 0/0</div>
      <div id="skill-bar"></div>
      <button id="btn-cancel-selection" aria-label="取消選擇">取消選擇 (Esc)</button>
    </div>

    <!-- Logs -->
    <details id="log-panel">
      <summary id="log-toggle">📜 戰鬥日誌</summary>
      <ul id="log-list" aria-live="polite"></ul>
    </details>

    <!-- Floating + Overlay -->
    <div id="target-overlay" aria-hidden="true"></div>
    <div id="floating-indicators"></div>

    <!-- Bottom Controls -->
    <div class="battle-controls">
      <button class="battle-btn speed" id="btn-speed" aria-label="切換速度">⏩ 1x</button>
      <button class="battle-btn pause" id="btn-pause-bottom" aria-label="暫停/繼續">⏸️ 暫停</button>
    </div>

    <!-- End Overlay -->
    <div id="end-overlay" style="display:none; position: fixed; inset:0; background: rgba(0,0,0,.35); z-index: 20; display:none; align-items:center; justify-content:center;">
      <div style="background:#fff; border:3px solid #8b4513; border-radius:10px; padding:16px 20px; box-shadow: 0 10px 24px rgba(0,0,0,.3); text-align:center;">
        <div id="end-text" style="font-size:18px; font-weight:bold; margin-bottom:8px;">戰鬥結束</div>
        <div style="font-size:12px; opacity:.8;">重新整理頁面可重置戰鬥</div>
      </div>
    </div>
  </div>

  <script>
    // 占位資料初始化
    const state = {
      battle: {
        phase: "idle", // idle | selecting-skill | selecting-target | resolving | ended
        isPaused: false,
        speed: 1, // 1 | 2 | 4
        turnIndex: 0,
        currentUnitId: "p1",
        turnQueue: ["p1","e3","p2","e1","p3","e2"],
        selection: { skillId: null, targets: [] },
        logs: []
      },
      units: {
        p1: { id:"p1", team:"player", name:"火龍戰士", level:5, icon:"🔥", race:"Dragon", rarity:4,
              stats:{ hp:300, maxHp:300, mp:40, maxMp:60, atk:120, spd:15 },
              tags:["Warrior","Fire"], status:[{ statusId:"haste", stacks:1, duration:2 }], skills:["strike","fireball","firestorm","guard"], alive:true },
        p2: { id:"p2", team:"player", name:"月光射手", level:3, icon:"🏹", race:"Elf", rarity:3,
              stats:{ hp:220, maxHp:220, mp:35, maxMp:50, atk:85, spd:18 },
              tags:["Archer"], status:[], skills:["strike","haste","guard"], alive:true },
        p3: { id:"p3", team:"player", name:"聖騎士", level:4, icon:"🛡️", race:"Human", rarity:3,
              stats:{ hp:260, maxHp:260, mp:30, maxMp:45, atk:75, spd:12 },
              tags:["Warrior","Support"], status:[], skills:["strike","guard","haste"], alive:true },
        e1: { id:"e1", team:"enemy", name:"狼人", level:4, icon:"🐺", race:"Beast", rarity:2,
              stats:{ hp:200, maxHp:200, mp:20, maxMp:20, atk:65, spd:12 },
              tags:["Beast"], status:[], skills:["strike"], alive:true },
        e2: { id:"e2", team:"enemy", name:"食人魔", level:5, icon:"🧌", race:"Ogre", rarity:3,
              stats:{ hp:250, maxHp:250, mp:20, maxMp:20, atk:85, spd:8 },
              tags:["Brute"], status:[], skills:["strike","guard"], alive:true },
        e3: { id:"e3", team:"enemy", name:"蜘蛛", level:3, icon:"🕷️", race:"Beast", rarity:2,
              stats:{ hp:180, maxHp:180, mp:20, maxMp:20, atk:45, spd:18 },
              tags:["Beast"], status:[], skills:["strike"], alive:true }
      },
      skills: {
        strike:    { id:"strike",    name:"強擊",    type:"attack", power:1.0, mpCost:0,  target:"single-enemy", cooldown:0, currentCooldown:0, appliesStatus:null },
        fireball:  { id:"fireball",  name:"火球",    type:"attack", power:1.2, mpCost:15, target:"single-enemy", cooldown:1, currentCooldown:0, appliesStatus:null },
        firestorm: { id:"firestorm", name:"地獄火雨",type:"attack", power:0.6, mpCost:50, target:"all-enemy",    cooldown:3, currentCooldown:0, appliesStatus:null },
        guard:     { id:"guard",     name:"防禦",    type:"buff",   power:0,   mpCost:0,  target:"self",          cooldown:1, currentCooldown:0, appliesStatus:"guarded" },
        haste:     { id:"haste",     name:"加速",    type:"buff",   power:0,   mpCost:10, target:"single-ally",   cooldown:2, currentCooldown:0, appliesStatus:"haste" }
      },
      statuses: {
        haste:   { id:"haste",   name:"加速",   kind:"buff", mods:{ spdPct:0.3 },   dot:null, hot:null },
        guarded: { id:"guarded", name:"防禦",   kind:"buff", mods:{ dmgTakenPct:-0.3 }, dot:null, hot:null }
      },
      cooldowns: {} // per-unit: { [unitId]: { [skillId]: number } }
    };

    // 初始化 per-unit 技能冷卻
    for (const id of Object.keys(state.units)) {
      state.cooldowns[id] = {};
      const u = state.units[id];
      (u.skills || []).forEach(sid => state.cooldowns[id][sid] = 0);
    }

    // DOM refs
    const $enemyCards = document.getElementById('enemy-cards');
    const $playerCards = document.getElementById('player-cards');
    const $enemyCount = document.getElementById('enemy-count');
    const $enemyTotalHp = document.getElementById('enemy-total-hp');
    const $playerCount = document.getElementById('player-count');
    const $playerTotalHp = document.getElementById('player-total-hp');
    const $turnQueue = document.getElementById('turn-queue');
    const $nextAction = document.getElementById('next-action');
    const $actionBar = document.getElementById('action-bar');
    const $actionMp = document.getElementById('action-mp');
    const $skillBar = document.getElementById('skill-bar');
    const $btnCancel = document.getElementById('btn-cancel-selection');
    const $logList = document.getElementById('log-list');
    const $logPanel = document.getElementById('log-panel');
    const $targetOverlay = document.getElementById('target-overlay');
    const $floating = document.getElementById('floating-indicators');
    const $btnPauseTop = document.getElementById('btn-pause');
    const $btnPauseBottom = document.getElementById('btn-pause-bottom');
    const $btnSpeed = document.getElementById('btn-speed');
    const $endOverlay = document.getElementById('end-overlay');
    const $endText = document.getElementById('end-text');

    // Helpers
    const getUnit = id => state.units[id];
    const isAlive = id => getUnit(id).alive && getUnit(id).stats.hp > 0;
    const teamUnits = team => Object.values(state.units).filter(u => u.team === team);
    const aliveTeamUnits = team => teamUnits(team).filter(u => u.alive);
    const percent = (part, whole) => Math.max(0, Math.min(100, Math.round((part / whole) * 100)));

    function effectiveSpd(u) {
      const hasteStacks = (u.status || []).find(s => s.statusId === 'haste');
      const bonus = hasteStacks ? state.statuses.haste.mods.spdPct : 0;
      return Math.round(u.stats.spd * (1 + bonus));
    }

    // Rendering
    function createCard(u) {
      const card = document.createElement('div');
      card.className = `battle-card ${u.team}`;
      card.setAttribute('data-unit-id', u.id);
      card.setAttribute('tabindex', '0');

      const level = document.createElement('div');
      level.className = 'card-level';
      level.textContent = u.level;

      const icon = document.createElement('div');
      icon.className = 'card-icon';
      icon.textContent = u.icon;

      const name = document.createElement('div');
      name.className = 'card-name';
      name.textContent = u.name;

      const stats = document.createElement('div');
      stats.className = 'card-stats';
      const atk = document.createElement('span'); atk.className = 'atk'; atk.textContent = `⚔️${u.stats.atk}`;
      const spd = document.createElement('span'); spd.className = 'spd'; spd.textContent = `⚡${u.stats.spd}`;
      stats.append(atk, spd);

      const hpBar = document.createElement('div');
      hpBar.className = 'hp-bar';
      const hpFill = document.createElement('div');
      hpFill.className = 'hp-fill' + (u.team === 'player' ? ' player' : '');
      hpFill.style.width = `${percent(u.stats.hp, u.stats.maxHp)}%`;
      hpBar.appendChild(hpFill);

      const statusRow = document.createElement('div');
      statusRow.className = 'status-row';
      // initial statuses
      (u.status || []).forEach(s => {
        const i = document.createElement('span');
        i.className = 'status-icon';
        i.dataset.statusId = s.statusId;
        i.title = `${state.statuses[s.statusId]?.name || s.statusId} x${s.stacks} (${s.duration})`;
        i.textContent = s.statusId === 'haste' ? '⚡' : '🛡️';
        statusRow.appendChild(i);
      });

      card.append(level, icon, name, stats, hpBar, statusRow);
      if (!u.alive) card.classList.add('ko');
      return card;
    }

    function renderUnits() {
      $enemyCards.innerHTML = '';
      $playerCards.innerHTML = '';
      teamUnits('enemy').forEach(u => $enemyCards.appendChild(createCard(u)));
      teamUnits('player').forEach(u => $playerCards.appendChild(createCard(u)));
      updateCounts();
    }

    function updateCounts() {
      const eAlive = aliveTeamUnits('enemy').length, eAll = teamUnits('enemy').length;
      const pAlive = aliveTeamUnits('player').length, pAll = teamUnits('player').length;
      const eHp = aliveTeamUnits('enemy').reduce((s,u)=>s+u.stats.hp,0);
      const pHp = aliveTeamUnits('player').reduce((s,u)=>s+u.stats.hp,0);
      $enemyCount.textContent = `${eAlive}/${eAll}`;
      $playerCount.textContent = `${pAlive}/${pAll}`;
      $enemyTotalHp.textContent = eHp;
      $playerTotalHp.textContent = pHp;
    }

    function renderTurnQueue() {
      $turnQueue.innerHTML = '';
      state.battle.turnQueue.forEach((id, idx) => {
        if (!isAlive(id)) return;
        const u = getUnit(id);
        const d = document.createElement('div');
        d.className = `action-indicator ${u.team}` + (idx === 0 ? ' active' : '');
        d.setAttribute('data-unit-id', id);
        d.setAttribute('role', 'listitem');
        d.title = `${u.name}`;
        d.textContent = u.team === 'player' ? '你' : '敵';
        $turnQueue.appendChild(d);
      });

      const next = getUnit(state.battle.turnQueue[0]);
      const seconds = (2 / state.battle.speed).toFixed(1);
      $nextAction.textContent = `⚡ 下一個: ${next ? next.name : '-'} (${seconds}秒)`;
      highlightActiveCard();
    }

    function highlightActiveCard() {
      document.querySelectorAll('.battle-card').forEach(el => el.classList.remove('active-turn'));
      const curId = state.battle.turnQueue[0];
      const el = document.querySelector(`.battle-card[data-unit-id="${curId}"]`);
      if (el) el.classList.add('active-turn');
    }

    function renderActionBar() {
      const curId = state.battle.turnQueue[0];
      const actor = getUnit(curId);
      if (!actor || actor.team !== 'player' || !actor.alive || state.battle.phase === 'ended') {
        $actionBar.style.display = 'none';
        return;
      }
      $actionBar.style.display = 'block';
      $actionMp.textContent = `MP ${actor.stats.mp}/${actor.stats.maxMp}`;
      $skillBar.innerHTML = '';
      (actor.skills || []).forEach(sid => {
        const def = state.skills[sid];
        const btn = document.createElement('button');
        btn.className = 'skill-btn';
        btn.setAttribute('data-skill-id', sid);
        btn.setAttribute('data-target', def.target);
        btn.setAttribute('aria-label', `${def.name} 消耗${def.mpCost} 冷卻${def.cooldown}`);
        const name = document.createElement('span'); name.className='skill-name'; name.textContent = def.name;
        const cost = document.createElement('span'); cost.className='skill-cost'; cost.textContent = `MP ${def.mpCost}`;
        const cd = document.createElement('span'); cd.className='skill-cd'; cd.textContent = `CD ${state.cooldowns[actor.id][sid] || 0}`;
        btn.append(name, cost, cd);

        // enabled/disabled rules
        const canUse = actor.alive && actor.stats.mp >= def.mpCost && (state.cooldowns[actor.id][sid] || 0) === 0 && state.battle.phase === 'idle';
        if (!canUse) {
          btn.classList.add('disabled');
          btn.setAttribute('aria-disabled', 'true');
          if (actor.stats.mp < def.mpCost) btn.title = 'MP 不足';
          else if ((state.cooldowns[actor.id][sid] || 0) > 0) btn.title = '冷卻中';
          else btn.title = '不可使用';
        }

        btn.addEventListener('click', () => onSelectSkill(actor.id, sid));
        $skillBar.appendChild(btn);
      });

      // cancel button
      $btnCancel.style.display = state.battle.phase === 'selecting-target' ? 'inline-block' : 'none';
    }

    // Selection & overlay
    function setTargetingMode(actorId, targetType) {
      $targetOverlay.style.display = 'block'; // dim only
      // mark targetables
      document.querySelectorAll('.battle-card').forEach(el => {
        const uid = el.getAttribute('data-unit-id');
        const u = getUnit(uid);
        el.classList.remove('targetable','untargetable');
        let can = false;
        if (!u.alive) {
          el.classList.add('untargetable','ko');
          return;
        }
        if (targetType === 'single-enemy') can = getUnit(actorId).team !== u.team;
        if (targetType === 'single-ally') can = getUnit(actorId).team === u.team;
        if (can) el.classList.add('targetable'); else el.classList.add('untargetable');
      });
      $btnCancel.style.display = 'inline-block';
    }

    function clearTargetingMode() {
      $targetOverlay.style.display = 'none';
      document.querySelectorAll('.battle-card').forEach(el => el.classList.remove('targetable','untargetable','selected'));
      $btnCancel.style.display = 'none';
      state.battle.selection.targets = [];
      state.battle.phase = 'idle';
      renderActionBar();
    }

    function onSelectSkill(actorId, skillId) {
      const actor = getUnit(actorId);
      const def = state.skills[skillId];
      if (!actor.alive) return;
      // re-check enable
      const cd = state.cooldowns[actorId][skillId] || 0;
      if (actor.stats.mp < def.mpCost || cd > 0 || state.battle.phase !== 'idle') return;

      state.battle.selection.skillId = skillId;
      // Direct resolve types
      if (def.target === 'self') {
        resolveAction(actorId, skillId, [actorId]);
      } else if (def.target === 'all-enemy') {
        const targets = aliveTeamUnits(actor.team === 'player' ? 'enemy' : 'player').map(u=>u.id);
        resolveAction(actorId, skillId, targets);
      } else {
        // need target select
        state.battle.phase = 'selecting-target';
        renderActionBar();
        setTargetingMode(actorId, def.target);
      }
    }

    function onSelectTarget(targetId) {
      const actorId = state.battle.turnQueue[0];
      const skillId = state.battle.selection.skillId;
      if (!skillId) return;
      const def = state.skills[skillId];
      if (def.target === 'single-enemy' || def.target === 'single-ally') {
        resolveAction(actorId, skillId, [targetId]);
      }
    }

    // Resolve
    function resolveAction(actorId, skillId, targets) {
      const actor = getUnit(actorId);
      const def = state.skills[skillId];
      if (!actor || !actor.alive) return;
      state.battle.phase = 'resolving';

      // MP & cooldown
      actor.stats.mp = Math.max(0, actor.stats.mp - def.mpCost);
      state.cooldowns[actorId][skillId] = def.cooldown;

      // Apply to each target
      const logParts = [];
      targets.forEach(tid => {
        const target = getUnit(tid);
        if (!target || !target.alive) return;

        if (def.type === 'attack') {
          let dmg = Math.round(actor.stats.atk * def.power);
          // guarded reduces 30%
          const guarded = (target.status || []).some(s => s.statusId === 'guarded');
          if (guarded) dmg = Math.round(dmg * (1 + state.statuses.guarded.mods.dmgTakenPct));

          target.stats.hp = Math.max(0, target.stats.hp - dmg);
          spawnFloat(tid, `-${dmg}`, 'dmg');
          damageAnimation(tid);

          logParts.push(`${actor.name} 對 ${target.name} 使用 ${def.name} 造成 ${dmg} 傷害`);

          if (target.stats.hp <= 0) {
            target.alive = false;
            onUnitKo(tid);
          }
        } else if (def.type === 'buff') {
          if (def.appliesStatus) {
            const toApply = { statusId: def.appliesStatus, stacks: 1, duration: 2 };
            // self or ally
            target.status = target.status || [];
            target.status.push(toApply);
            addStatusIcon(tid, toApply);
            spawnFloat(tid, def.appliesStatus === 'haste' ? '加速' : '防禦', 'buff');
            logParts.push(`${actor.name} 對 ${target.name} 施放 ${def.name}`);
          }
        }
      });

      appendLog(logParts.join('；'));
      updateCounts();
      syncCards();
      renderActionBar();

      // clear selection + advance
      state.battle.selection.skillId = null;
      state.battle.selection.targets = [];
      clearTargetingMode();

      // Check victory
      const winner = checkEnd();
      if (winner) {
        endBattle(winner);
        return;
      }

      // advance turn
      advanceTurn();
    }

    function onUnitKo(unitId) {
      // Remove from queue
      state.battle.turnQueue = state.battle.turnQueue.filter(id => id !== unitId);
      // Update DOM
      const card = document.querySelector(`.battle-card[data-unit-id="${unitId}"]`);
      if (card) card.classList.add('ko');
      updateCounts();
    }

    function addStatusIcon(unitId, s) {
      const card = document.querySelector(`.battle-card[data-unit-id="${unitId}"]`);
      if (!card) return;
      const row = card.querySelector('.status-row');
      const icon = document.createElement('span');
      icon.className = 'status-icon';
      icon.dataset.statusId = s.statusId;
      icon.title = `${state.statuses[s.statusId]?.name || s.statusId} (${s.duration})`;
      icon.textContent = s.statusId === 'haste' ? '⚡' : '🛡️';
      row.appendChild(icon);
    }

    function damageAnimation(unitId) {
      const card = document.querySelector(`.battle-card[data-unit-id="${unitId}"]`);
      if (!card) return;
      card.classList.add('damage-animation');
      setTimeout(()=>card.classList.remove('damage-animation'), 350);
    }

    function spawnFloat(unitId, text, kind='dmg') {
      const card = document.querySelector(`.battle-card[data-unit-id="${unitId}"]`);
      if (!card) return;
      const rect = card.getBoundingClientRect();
      const root = document.querySelector('.battle-container').getBoundingClientRect();
      const span = document.createElement('span');
      span.className = `float-indicator ${kind}`;
      span.textContent = text;
      // position above card
      span.style.top = `${rect.top - root.top + 4}px`;
      span.style.left = `${rect.left - root.left + rect.width/2}px`;
      document.getElementById('floating-indicators').appendChild(span);
      setTimeout(()=> span.remove(), 650);
    }

    function appendLog(text) {
      state.battle.logs.push(text);
      const li = document.createElement('li');
      li.className = 'log-item';
      li.textContent = text;
      $logList.appendChild(li);
    }

    // Turn management
    function advanceTurn() {
      // Shift current to end
      if (state.battle.turnQueue.length === 0) return;
      const finished = state.battle.turnQueue.shift();
      if (isAlive(finished)) state.battle.turnQueue.push(finished);

      // Next actor start of turn: decrement cooldowns and durations
      const curId = state.battle.turnQueue[0];
      if (!curId) return;

      // cooldown decrement for the actor
      const cds = state.cooldowns[curId];
      for (const sid of Object.keys(cds)) cds[sid] = Math.max(0, (cds[sid] || 0) - 1);

      // status duration tick for everyone (simplify: tick when they start a turn)
      Object.values(state.units).forEach(u => {
        if (!u.alive) return;
        (u.status || []).forEach(s => s.duration--);
        u.status = (u.status || []).filter(s => s.duration > 0);
        // sync icons
        const card = document.querySelector(`.battle-card[data-unit-id="${u.id}"]`);
        if (card) {
          const row = card.querySelector('.status-row');
          row.innerHTML = '';
          (u.status || []).forEach(s => addStatusIcon(u.id, s));
        }
      });

      // Reorder by speed (簡化處理): keep current head, sort the rest by effective speed desc
      const head = state.battle.turnQueue[0];
      const rest = state.battle.turnQueue.slice(1).filter(isAlive);
      rest.sort((a,b)=> effectiveSpd(getUnit(b)) - effectiveSpd(getUnit(a)));
      state.battle.turnQueue = [head, ...rest];

      state.battle.phase = 'idle';
      renderTurnQueue();
      renderActionBar();
    }

    function checkEnd() {
      const pAlive = aliveTeamUnits('player').length;
      const eAlive = aliveTeamUnits('enemy').length;
      if (pAlive === 0) return 'defeat';
      if (eAlive === 0) return 'victory';
      return null;
    }
    function endBattle(result) {
      state.battle.phase = 'ended';
      $actionBar.style.display = 'none';
      $endText.textContent = result === 'victory' ? '🎉 勝利！' : '💀 戰敗';
      document.getElementById('end-overlay').style.display = 'flex';
    }

    function syncCards() {
      // Sync HP bars and KO state
      document.querySelectorAll('.battle-card').forEach(card => {
        const id = card.getAttribute('data-unit-id');
        const u = getUnit(id);
        const fill = card.querySelector('.hp-fill');
        if (fill) fill.style.width = `${percent(u.stats.hp, u.stats.maxHp)}%`;
        if (!u.alive) card.classList.add('ko');
        else card.classList.remove('ko');
      });
    }

    // Enemy AI
    function enemyAct() {
      const actorId = state.battle.turnQueue[0];
      const actor = getUnit(actorId);
      if (!actor || actor.team !== 'enemy' || !actor.alive) return;
      // choose skill priority: attack > strike > guard
      const skills = actor.skills || [];
      let choice = null;
      // try any attack skill usable
      for (const sid of skills) {
        const d = state.skills[sid];
        if (d.type === 'attack' && actor.stats.mp >= d.mpCost && (state.cooldowns[actorId][sid]||0) === 0) { choice = sid; break; }
      }
      if (!choice) {
        if (actor.stats.mp >= 0 && (state.cooldowns[actorId]['strike']||0) === 0) choice = 'strike';
      }
      if (!choice && skills.includes('guard') && (state.cooldowns[actorId]['guard']||0)===0) choice = 'guard';
      if (!choice) choice = 'strike';

      const def = state.skills[choice];
      let targets = [];
      if (def.target === 'single-enemy') {
        const list = aliveTeamUnits('player');
        if (list.length === 0) return;
        // pick lowest hp
        list.sort((a,b)=> a.stats.hp - b.stats.hp);
        targets = [list[0].id];
      } else if (def.target === 'all-enemy') {
        targets = aliveTeamUnits('player').map(u=>u.id);
      } else if (def.target === 'self') {
        targets = [actorId];
      } else if (def.target === 'single-ally') {
        const list = aliveTeamUnits('enemy');
        // pick self if available
        targets = [(list[0]||actor).id];
      }
      resolveAction(actorId, choice, targets);
    }

    // Loop
    let loopHandle = null;
    function loopTick() {
      if (state.battle.phase === 'ended') return;
      if (!state.battle.isPaused) {
        const actor = getUnit(state.battle.turnQueue[0]);
        if (actor && actor.team === 'enemy' && state.battle.phase === 'idle') {
          enemyAct();
        }
      }
      const delay = Math.max(500, Math.round(2200 / state.battle.speed));
      loopHandle = setTimeout(loopTick, delay);
    }

    // Controls
    function syncPauseButtons() {
      const text = state.battle.isPaused ? '▶️ 繼續' : '⏸️ 暫停';
      $btnPauseTop.textContent = state.battle.isPaused ? '▶️' : '⏸️';
      $btnPauseBottom.textContent = text;
      $btnPauseBottom.style.background = state.battle.isPaused ? 'rgba(34,139,34,.85)' : 'rgba(220,38,38,.85)';
    }
    $btnPauseTop.addEventListener('click', ()=> { state.battle.isPaused = !state.battle.isPaused; syncPauseButtons(); });
    $btnPauseBottom.addEventListener('click', ()=> { state.battle.isPaused = !state.battle.isPaused; syncPauseButtons(); });

    document.getElementById('btn-sfx').addEventListener('click', ()=>{
      // 只是切換圖示狀態（占位）
      const el = document.getElementById('btn-sfx');
      const on = el.textContent.includes('🔊');
      el.textContent = on ? '🔈' : '🔊';
      appendLog(`音效${on ? '關閉' : '開啟'}`);
    });
    document.getElementById('btn-settings').addEventListener('click', ()=>{
      appendLog('開啟設定（占位）');
      alert('設定面板（占位）');
    });

    $btnSpeed.addEventListener('click', ()=>{
      const next = state.battle.speed === 1 ? 2 : state.battle.speed === 2 ? 4 : 1;
      state.battle.speed = next;
      $btnSpeed.textContent = `⏩ ${next}x`;
      renderTurnQueue();
    });

    $btnCancel.addEventListener('click', clearTargetingMode);
    document.addEventListener('keydown', (e)=>{
      if (e.key === 'Escape' && state.battle.phase === 'selecting-target') {
        clearTargetingMode();
      }
    });

    // Click handlers for selecting target
    function attachCardClickDelegation() {
      document.getElementById('enemy-cards').addEventListener('click', e=>{
        const card = e.target.closest('.battle-card');
        if (!card) return;
        if (state.battle.phase === 'selecting-target' && card.classList.contains('targetable')) {
          card.classList.add('selected');
          onSelectTarget(card.getAttribute('data-unit-id'));
        }
      });
      document.getElementById('player-cards').addEventListener('click', e=>{
        const card = e.target.closest('.battle-card');
        if (!card) return;
        if (state.battle.phase === 'selecting-target' && card.classList.contains('targetable')) {
          card.classList.add('selected');
          onSelectTarget(card.getAttribute('data-unit-id'));
        }
      });
    }

    // Bootstrap
    function init() {
      renderUnits();
      renderTurnQueue();
      renderActionBar();
      attachCardClickDelegation();
      // Ensure current active-turn visual
      highlightActiveCard();
      // auto loop
      loopTick();
      // logs default collapsed on small screens: details不設open即為收合
    }
    init();

    // Public test notes (操作提示寫在 log)
    appendLog('提示：Flow A 測試：在 p1 回合點擊「強擊」後點選狼人卡。');
    appendLog('提示：Flow B 測試：p1 的「地獄火雨」因 MP 不足應為停用。');
    appendLog('提示：Flow C 測試：在 p3 回合使用「加速」點選 p2，下一輪可見速度優勢。');
  </script>
</body>
</html>