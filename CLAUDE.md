# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 項目概述

這是一個混合架構的卡牌戰鬥遊戲項目，直接基於 Expo 和 React Native：
- **主要項目**: Expo + React Native 手機應用（直接在根目錄）
- **layout/**: HTML/CSS UI 設計原型和參考
- **types/**: TypeScript 類型定義
- **components/**: React 組件

## 開發指令

### Expo React Native 應用
```bash
# 安裝依賴
npm install

# 開發運行
npm start
# 或
expo start

# 平台特定運行
npm run android  # Android 模擬器
npm run ios      # iOS 模擬器  
npm run web      # Web 瀏覽器
```

## 架構設計

### Expo React Native 應用架構
- **技術棧**: React Native 0.79.5, Expo SDK 53, TypeScript
- **JavaScript 引擎**: Hermes
- **主入口**: index.ts
- **狀態管理**: React Context + hooks
- **類型支持**: TypeScript 嚴格模式
- **配置管理**: CSV 檔案 + ConfigContext

### 項目結構
```
WebPetting/
├── App.tsx             # 主應用組件
├── index.ts            # 應用入口點
├── app.json            # Expo 配置
├── package.json        # 依賴管理
├── tsconfig.json       # TypeScript 配置
├── metro.config.js     # Metro 打包配置
├── assets/             # 圖像資源
│   ├── icon.png        # 應用圖標
│   ├── splash-icon.png # 啟動畫面
│   └── favicon.png     # Web favicon
├── components/         # React 組件
│   └── BattleScreen.tsx # 戰鬥界面組件
├── config/             # 遊戲配置檔案 (CSV)
│   ├── AIBehaviorConfig.csv # AI 行為配置
│   ├── CardConfig.csv  # 卡牌配置
│   ├── DropConfig.csv  # 掉落配置
│   ├── EnemyDeckConfig.csv # 敵方牌組配置
│   ├── GachaConfig.csv # 抽卡配置
│   ├── PlayerTeamConfig.csv # 玩家隊伍配置
│   ├── SkillConfig.csv # 技能配置
│   └── StageConfig.csv # 關卡配置
├── constants/          # 常量定義
│   └── Colors.ts       # 顏色常量
├── contexts/           # React Context
│   └── ConfigContext.tsx # 配置上下文
├── types/              # TypeScript 類型定義
│   ├── battle.ts       # 戰鬥相關類型
│   ├── gacha.ts        # 抽卡相關類型
│   ├── ui.ts           # UI 相關類型
│   ├── config.ts       # 配置類型
│   ├── constants.ts    # 常量類型
│   └── utils.ts        # 工具類型
├── docs/               # 遊戲設計文檔
│   ├── game_design.md  # 完整遊戲設計規格
│   ├── handdrawn-card-layout-guide.md
│   └── implementation-summary.md
└── layout/             # HTML/CSS UI 設計原型
    ├── main-menu.html  # 主界面佈局
    ├── battle-ui.html  # 戰鬥界面設計
    ├── card-ui.html    # 卡牌組件設計
    └── gacha-ui.html   # 抽卡界面設計
```

### 設計系統
- **方向**: 僅支持直向模式 (Portrait)
- **介面風格**: Light mode (預設)
- **響應式**: 針對手機直向模式優化
- **觸控**: 最小 44pt 觸控區域
- **跨平台**: 支持 iOS、Android、Web

## 遊戲設計參考

### 核心概念
基於 `docs/game_design.md` 的設計，這是一個自動戰鬥策略卡牌收集遊戲：
- **全自動戰鬥**: 戰鬥完全自動進行
- **速度驅動**: 基於卡牌速度屬性的行動順序
- **卡牌收集**: 抽卡系統和卡牌強化
- **手機優化**: 專為5-7吋螢幕設計

### UI 設計參考
`layout/` 目錄包含 HTML/CSS 設計原型：
- `main-menu.html`: 主界面佈局
- `battle-ui.html`: 戰鬥界面設計
- `card-ui.html`: 卡牌組件設計
- `gacha-ui.html`: 抽卡界面設計

## 開發規範

### Bug 修復方法論
在進行 Bug 修復時，必須需要進行深入思考和全面分析，對於每個 Bug，至少需要詢問自己以下問題：

1. **問題描述**: 問題是什麼？詳細描述 Bug 的表現
2. **預期目標**: 目標是什麼？預期的正確結果是什麼？
3. **歷史分析**: 如果不是第一次修復此 Bug，之前遺漏了什麼？
4. **線索搜尋**: 在上下文或腳本中，可以找到哪些線索幫助實現任務？
5. **根因分析**: 可能導致 Bug 的原因是什麼？列出所有可能性
6. **深度檢查**: 如果當前分析不是主要原因，是否需要尋找更多線索或代碼？
7. **解決驗證**: 已做出的更改是什麼？為什麼這些更改可以修復 Bug？

### 代碼風格
- 遵循 TypeScript 嚴格模式
- 使用 ESLint 進行代碼檢查
- 組件使用函數式組件 + hooks
- 檔名使用 PascalCase (組件) 或 camelCase (工具)

### 開發流程
1. 開發前先運行 `npm install` 確保依賴最新
2. 使用 `expo start` 開始開發
3. 功能完成後在多個平台測試 (iOS/Android/Web)
4. 參考設計文檔確保符合遊戲設計理念
5. 檢查 TypeScript 類型定義是否正確