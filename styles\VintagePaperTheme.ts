/**
 * Vintage Paper Theme - 基於 battle-ui-vintage-paper.html 的設計系統
 * 溫暖米色調色板 + 手繪風格
 */

export const VintagePaperTheme = {
  // 主要顏色 - 對應 CSS :root 變量
  colors: {
    // 背景色
    bg0: '#f7f1e3',      // --bg-0: 背景紙
    bg1: '#efe7d4',      // --bg-1: 次層紙
    paperEdge: '#e4d6b9', // --paper-edge
    
    // 文字和邊框
    ink: '#3b2b22',      // --ink: 手寫墨色
    
    // 強調色
    accent: '#c46a2b',   // --accent: 焦糖/赭石（攻擊）
    accent2: '#5b7a8d',  // --accent-2: 互補的藍灰（提示/技能）
    
    // 語義色
    good: '#2f6f4f',     // --good: 我方/治癒
    bad: '#a7302b',      // --bad: 敵方/傷害
    
    // 透明度變體
    enemyBg: 'rgba(167, 48, 43, 0.06)',
    playerBg: 'rgba(47, 111, 79, 0.06)',
    actionDividerBg: 'rgba(196, 106, 43, 0.08)',
    
    // 卡片邊框
    enemyBorder: 'rgba(167, 48, 43, 0.85)',
    playerBorder: 'rgba(47, 111, 79, 0.85)',
    
    // 血條漸變
    enemyHpGradient: ['#dc4b3f', '#a7302b'],
    playerHpGradient: ['#2e7d4f', '#1e5f3a'],
  },
  
  // 陰影
  shadows: {
    card: {
      shadowColor: 'rgba(59, 43, 34, 0.15)',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 1,
      shadowRadius: 16,
      elevation: 6,
    },
    button: {
      shadowColor: 'rgba(59, 43, 34, 0.25)',
      shadowOffset: { width: 2, height: 2 },
      shadowOpacity: 1,
      shadowRadius: 0,
      elevation: 2,
    },
    header: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3,
    },
  },
  
  // 邊框半徑
  radius: {
    small: 6,
    medium: 8,
    large: 10,
    round: 50,
  },
  
  // 字體大小（基於螢幕高度的比例）
  typography: {
    // 標題
    stageTitle: (height: number) => Math.max(16, height * 0.025),
    sectionTitle: (height: number) => Math.max(14, height * 0.018),
    
    // 卡片
    cardName: (cardHeight: number) => Math.max(8, cardHeight * 0.08),
    cardIcon: (cardHeight: number) => Math.max(16, cardHeight * 0.15),
    cardStats: (cardHeight: number) => Math.max(7, cardHeight * 0.07),
    
    // 導航
    navIcon: (height: number) => Math.max(16, height * 0.02),
    navLabel: (height: number) => Math.max(8, height * 0.012),
  },
  
  // 間距
  spacing: {
    xs: 2,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    xxl: 20,
  },
  
  // 卡片尺寸（基於可用空間）
  cardSizing: {
    // 標準卡片比例 (3:4)
    aspectRatio: 3 / 4,
    
    // 最小/最大尺寸
    minWidth: 70,
    maxWidth: 90,
    minHeight: 95,
    maxHeight: 120,
    
    // 網格設定
    gridColumns: 3,
    gridGap: 6,
    gridPadding: 8,
  },
  
  // 佈局比例
  layout: {
    headerRatio: 0.08,    // 8% 給標題
    sectionRatio: 0.46,   // 46% 給每個戰鬥區域
  },
};

// 輔助函數：計算卡片尺寸
export const calculateCardSize = (screenWidth: number, sectionHeight: number) => {
  const { cardSizing } = VintagePaperTheme;
  const { gridColumns, gridGap, gridPadding, aspectRatio, minWidth, maxWidth, minHeight, maxHeight } = cardSizing;
  
  const rows = 3;
  const horizontalPadding = gridPadding * 2;
  const verticalPadding = 24; // 上下各 12
  const marginBottom = 6; // gridCell marginBottom
  
  const availableWidth = screenWidth - horizontalPadding;
  const sectionAvailableHeight = sectionHeight - verticalPadding - (rows - 1) * marginBottom;
  
  // 每列寬度為 32% 的容器寬度
  const cellWidth = availableWidth * 0.32;
  const maxHeightPerRow = Math.floor(sectionAvailableHeight / rows);
  
  // 保持 3:4 比例，但不超過可用空間
  const cardWidth = Math.min(cellWidth - 4, Math.floor(maxHeightPerRow * aspectRatio));
  const cardHeight = Math.min(maxHeightPerRow - 4, Math.floor(cardWidth / aspectRatio));
  
  return {
    cardWidth: Math.max(minWidth, Math.min(cardWidth, maxWidth)),
    cardHeight: Math.max(minHeight, Math.min(cardHeight, maxHeight))
  };
};

// 輔助函數：創建線性漸變（React Native 需要額外的庫）
export const createGradientColors = (colors: string[]) => colors;

// 輔助函數：計算百分比
export const percent = (part: number, whole: number) =>
  Math.max(0, Math.min(100, Math.round((part / whole) * 100)));
